[project]
name = "deep_agents_from_scratch"
version = "0.1.0"
description = "Build a deep agent from scratch"
requires-python = ">=3.11"
dependencies = [
"langgraph>=0.6.4",
"langchain>=0.3.0",
"langchain-openai>=0.2.0",
"langchain-anthropic>=0.3.0",
"langchain_community>=0.3.27",
"langchain_tavily>=0.2.7",
"langchain_mcp_adapters>=0.1.9",
"pydantic>=2.0.0",
"rich>=14.0.0",
"jupyter>=1.0.0",
"ipykernel>=6.20.0",
"tavily-python>=0.5.0",
"httpx>=0.28.1",
"markdownify>=1.2.0",
"deepagents>=0.0.3",
]

[project.optional-dependencies]
dev = ["mypy>=1.11.1", "ruff>=0.6.1"]

[build-system]
requires = ["setuptools>=73.0.0", "wheel"]
build-backend = "setuptools.build_meta"

[tool.setuptools]
packages = ["deep_agents_from_scratch"]

[tool.setuptools.package-dir]
"deep_agents_from_scratch" = "src/deep_agents_from_scratch"

[tool.setuptools.package-data]
"*" = ["py.typed"]

[tool.ruff]
lint.select = [
    "E",    # pycodestyle
    "F",    # pyflakes
    "I",    # isort
    "D",    # pydocstyle
    "D401", # First line should be in imperative mood
    "T201",
    "UP",
]
lint.ignore = [
    "UP006",
    "UP007",
    "UP035",
    "D417",
    "E501",
]

[tool.ruff.lint.per-file-ignores]
"tests/*" = ["D", "UP"]

[tool.ruff.lint.pydocstyle]
convention = "google"