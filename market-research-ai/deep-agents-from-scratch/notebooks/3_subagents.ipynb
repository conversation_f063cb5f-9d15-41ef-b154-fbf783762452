{"cells": [{"cell_type": "code", "execution_count": 1, "id": "ebda9f81", "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "from dotenv import load_dotenv\n", "\n", "load_dotenv(os.path.join(\"..\", \".env\"), override=True)\n", "\n", "%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "markdown", "id": "95bd95a5-4d68-41f4-a7ea-73e5429d43f2", "metadata": {}, "source": ["## Context Isolation: Sub-agents\n", "\n", "<img src=\"./assets/agent_header_subagent.png\" width=\"800\" style=\"display:block; margin-left:0;\">\n", "\n", "Agent context can grow quickly as conversations progress, leading to several long context-related problems. A primary issue is context clash or confusion, where mixed objectives within the same context window can lead to suboptimal performance. [Context isolation](https://blog.langchain.com/context-engineering-for-agents/) provides an effective solution by delegating tasks to [specialized sub-agents](https://www.anthropic.com/engineering/multi-agent-research-system), each operating within their own isolated context window. This approach prevents context clashes, confusion, poisoning, and dilution while enabling focused, specialized task execution.\n", "\n", "\n", "\n", "### Sub-agent delegation\n", "![./assets/subagents.png](./assets/subagents.png)\n", "The primary insight is that we can create sub-agents with different tool sets tailored to specific tasks. Each sub-agent is stored in a registry dictionary with `subagent_type` as the key, allowing the main agent to delegate work through a `task(description, subagent_type)` tool call. The sub-agent operates in complete isolation from the parent's context, and its results are returned as a `ToolMessage` to the parent agent, maintaining clean separation of concerns."]}, {"cell_type": "markdown", "id": "f777607e-b915-487f-a20b-f13aec436f1a", "metadata": {}, "source": ["## Step 1: Create Sub Agents\n", "\n", "Let's define how the user will specify sub agents\n", "```python\n", "from typing_extensions import TypedDict\n", "\n", "class SubAgent(TypedDict):\n", "    \"\"\"Configuration for a specialized sub-agent.\"\"\"\n", "\n", "    name: str\n", "    description: str\n", "    prompt: str\n", "    tools: NotRequired[list[str]]\n", "```\n", "\n", "We will use a list of these objects to create all the sub agents we have access to\n", "\n", "```python\n", "agents: list[SubAgent] = ...\n", "subagents = {\n", "    agent['name']: create_react_agent(\n", "        model=model,\n", "        prompt=agent['prompt'],\n", "        tools = get_tools(agent['tools']),\n", "        ...\n", "    )\n", "}\n", "```\n", "\n", "## Step 2: Create a tool to use Sub Agents\n", "\n", "Logically, should look something like:\n", "\n", "```python\n", "def task(\n", "    description: str  # The task the subagent should do\n", "    subagent_type: str  # Which subagent to use\n", "):\n", "    # Create new messages to pass to subagent - should just be the description\n", "    # Call sub agent\n", "    # Update state with both the subagents response AND any changes to file system\n", "```\n", "\n", "This ends up in full looking like:\n", "\n", "```python\n", "@tool(description=TASK_DESCRIPTION_PREFIX.format(other_agents=other_agents_string))\n", "def task(\n", "    description: str,\n", "    subagent_type: str,\n", "    state: Annotated[DeepAgentState, InjectedState],\n", "    tool_call_id: Annotated[str, InjectedToolCallId],\n", "):\n", "    \"\"\"Delegate a task to a specialized sub-agent with isolated context.\n", "\n", "    This creates a fresh context for the sub-agent containing only the task description,\n", "    preventing context pollution from the parent agent's conversation history.\n", "    \"\"\"\n", "    # Validate requested agent type exists\n", "    if subagent_type not in agents:\n", "        return f\"Error: invoked agent of type {subagent_type}, the only allowed types are {[f'`{k}`' for k in agents]}\"\n", "\n", "    # Get the requested sub-agent\n", "    sub_agent = agents[subagent_type]\n", "\n", "    # Create isolated context with only the task description\n", "    # This is the key to context isolation - no parent history\n", "    state[\"messages\"] = [{\"role\": \"user\", \"content\": description}]\n", "\n", "    # Execute the sub-agent in isolation\n", "    result = sub_agent.invoke(state)\n", "\n", "    # Return results to parent agent via Command state update\n", "    return Command(\n", "        update={\n", "            \"files\": result.get(\"files\", {}),  # Merge any file changes\n", "            \"messages\": [\n", "                # Sub-agent result becomes a ToolMessage in parent context\n", "                ToolMessage(\n", "                    result[\"messages\"][-1].content, tool_call_id=tool_call_id\n", "                )\n", "            ],\n", "        }\n", "    )\n", "\n", "return task\n", "```"]}, {"cell_type": "code", "execution_count": 2, "id": "9efa20a5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Overwriting ../src/deep_agents_from_scratch/task_tool.py\n"]}], "source": ["%%writefile ../src/deep_agents_from_scratch/task_tool.py\n", "\"\"\"Task delegation tools for context isolation through sub-agents.\n", "\n", "This module provides the core infrastructure for creating and managing sub-agents\n", "with isolated contexts. Sub-agents prevent context clash by operating with clean\n", "context windows containing only their specific task description.\n", "\"\"\"\n", "\n", "from typing import Annotated, NotRequired\n", "from typing_extensions import TypedDict\n", "\n", "from langchain_core.messages import ToolMessage\n", "from langchain_core.tools import BaseTool, InjectedToolCallId, tool\n", "from langgraph.prebuilt import InjectedState, create_react_agent\n", "from langgraph.types import Command\n", "\n", "from deep_agents_from_scratch.prompts import TASK_DESCRIPTION_PREFIX\n", "from deep_agents_from_scratch.state import DeepAgentState\n", "\n", "\n", "class SubAgent(TypedDict):\n", "    \"\"\"Configuration for a specialized sub-agent.\"\"\"\n", "\n", "    name: str\n", "    description: str\n", "    prompt: str\n", "    tools: NotRequired[list[str]]\n", "\n", "\n", "def _create_task_tool(tools, subagents: list[SubAgent], model, state_schema):\n", "    \"\"\"Create a task delegation tool that enables context isolation through sub-agents.\n", "\n", "    This function implements the core pattern for spawning specialized sub-agents with\n", "    isolated contexts, preventing context clash and confusion in complex multi-step tasks.\n", "\n", "    Args:\n", "        tools: List of available tools that can be assigned to sub-agents\n", "        subagents: List of specialized sub-agent configurations\n", "        model: The language model to use for all agents\n", "        state_schema: The state schema (typically DeepAgentState)\n", "\n", "    Returns:\n", "        A 'task' tool that can delegate work to specialized sub-agents\n", "    \"\"\"\n", "    # Create agent registry\n", "    agents = {}\n", "\n", "    # Build tool name mapping for selective tool assignment\n", "    tools_by_name = {}\n", "    for tool_ in tools:\n", "        if not isinstance(tool_, BaseTool):\n", "            tool_ = tool(tool_)\n", "        tools_by_name[tool_.name] = tool_\n", "\n", "    # Create specialized sub-agents based on configurations\n", "    for _agent in subagents:\n", "        if \"tools\" in _agent:\n", "            # Use specific tools if specified\n", "            _tools = [tools_by_name[t] for t in _agent[\"tools\"]]\n", "        else:\n", "            # Default to all tools\n", "            _tools = tools\n", "        agents[_agent[\"name\"]] = create_react_agent(\n", "            model, prompt=_agent[\"prompt\"], tools=_tools, state_schema=state_schema\n", "        )\n", "\n", "    # Generate description of available sub-agents for the tool description\n", "    other_agents_string = [\n", "        f\"- {_agent['name']}: {_agent['description']}\" for _agent in subagents\n", "    ]\n", "\n", "    @tool(description=TASK_DESCRIPTION_PREFIX.format(other_agents=other_agents_string))\n", "    def task(\n", "        description: str,\n", "        subagent_type: str,\n", "        state: Annotated[DeepAgentState, InjectedState],\n", "        tool_call_id: Annotated[str, InjectedToolCallId],\n", "    ):\n", "        \"\"\"Delegate a task to a specialized sub-agent with isolated context.\n", "\n", "        This creates a fresh context for the sub-agent containing only the task description,\n", "        preventing context pollution from the parent agent's conversation history.\n", "        \"\"\"\n", "        # Validate requested agent type exists\n", "        if subagent_type not in agents:\n", "            return f\"Error: invoked agent of type {subagent_type}, the only allowed types are {[f'`{k}`' for k in agents]}\"\n", "\n", "        # Get the requested sub-agent\n", "        sub_agent = agents[subagent_type]\n", "\n", "        # Create isolated context with only the task description\n", "        # This is the key to context isolation - no parent history\n", "        state[\"messages\"] = [{\"role\": \"user\", \"content\": description}]\n", "\n", "        # Execute the sub-agent in isolation\n", "        result = sub_agent.invoke(state)\n", "\n", "        # Return results to parent agent via Command state update\n", "        return Command(\n", "            update={\n", "                \"files\": result.get(\"files\", {}),  # Merge any file changes\n", "                \"messages\": [\n", "                    # Sub-agent result becomes a ToolMessage in parent context\n", "                    ToolMessage(\n", "                        result[\"messages\"][-1].content, tool_call_id=tool_call_id\n", "                    )\n", "                ],\n", "            }\n", "        )\n", "\n", "    return task"]}, {"cell_type": "markdown", "id": "10b27747-9a1b-4192-816a-beadf0fa4a72", "metadata": {}, "source": ["Now, you have a routine that will generate sub-agents as tools. Now, you can define specific sub-agents and allow the system to call them with the `task` tool.    \n", "Above, the `_create_task_tool` receives a list of type `SubAgent`. This list contains descriptions of the agents that are to be created. \n", "\n", "```python\n", "class SubAgent(TypedDict):\n", "    \"\"\"Configuration for a specialized sub-agent.\"\"\"\n", "\n", "    name: str\n", "    description: str\n", "    prompt: str\n", "    tools: NotRequired[list[str]]\n", "\n", "\n", "def _create_task_tool(tools, subagents: list[SubAgent], model, state_schema):\n", "    \"\"\"Create a task delegation tool that enables context isolation through sub-agents.\n", "\n", "```\n", "The `SubAgent` class defines the unique information needed to satisfy the dual role of a sub-agent. Sub-agents act as both tools and agents.  \n", "\n", "- **As tools**, they provide the supervisor agent with information about their capabilities and how they can be called.  \n", "- **As agents**, they require a prompt that describes how to carry out their tasks, along with a set of tools targeted for those tasks.  \n", "\n", "Below, you will create a research subagent. Its `description` informs the supervisor agent that a single task should be delegated to this sub-agent. The `SIMPLE_RESEARCH_INSTRUCTIONS` is a prompt that is used by the sub-agent to direct its research. In this example, it is brief, but for a general-purpose researcher, it could be much more detailed. The sub-agent is also supplied with a `web_search` tool to use during its research.  \n", "\n", "```python\n", "# Create research sub-agent\n", "research_sub_agent = {\n", "    \"name\": \"research-agent\",\n", "    \"description\": \"Delegate research to the sub-agent researcher. Only give this researcher one topic at a time.\",\n", "    \"prompt\": SIMPLE_RESEARCH_INSTRUCTIONS,\n", "    \"tools\": [\"web_search\"],\n", "}\n", "```\n", "\n", "Note that the sub-agent receives a specific task, along with the necessary tools to complete it. It operates in its own context, limited to the single task description. This [context-engineering](https://blog.langchain.com/context-engineering-for-agents/) approach ensures that the subagent’s working context remains free of context clashes, confusion, poisoning, and dilution.\n", "\n", "The supervisor agent prompt must now include a descripition of how to invoke and use these sub-agents. This is shown below. Note the *Available Tools* description and the instructions to use parallel research where applicable."]}, {"cell_type": "code", "execution_count": 3, "id": "1709b55c", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #000080; text-decoration-color: #000080\">╭──────────────────────────────────────────────────── </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">Prompt</span><span style=\"color: #000080; text-decoration-color: #000080\"> ─────────────────────────────────────────────────────╮</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  You can delegate tasks to sub-agents.                                                                          <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  <span style=\"color: #000080; text-decoration-color: #000080; font-weight: bold\">&lt;Task&gt;</span>                                                                                                         <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  Your role is to coordinate research by delegating specific research tasks to sub-agents.                       <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  <span style=\"color: #000080; text-decoration-color: #000080; font-weight: bold\">&lt;/Task&gt;</span>                                                                                                        <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  <span style=\"color: #000080; text-decoration-color: #000080; font-weight: bold\">&lt;Available Tools&gt;</span>                                                                                              <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  1. **task(description, subagent_type)**: Delegate research tasks to specialized sub-agents                     <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>     - description: Clear, specific research question or task                                                    <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>     - subagent_type: Type of agent to use (e.g., \"research-agent\")                                              <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  2. **think_tool(reflection)**: Reflect on the results of each delegated task and plan next steps.              <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>     - reflection: Your detailed reflection on the results of the task and next steps.                           <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  **PARALLEL RESEARCH**: When you identify multiple independent research directions, make multiple **task**      <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  tool calls in a single response to enable parallel execution. Use at most {max_concurrent_research_units}      <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  parallel agents per iteration.                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  <span style=\"color: #000080; text-decoration-color: #000080; font-weight: bold\">&lt;/Available Tools&gt;</span>                                                                                             <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  <span style=\"color: #000080; text-decoration-color: #000080; font-weight: bold\">&lt;Hard Limits&gt;</span>                                                                                                  <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  **Task Delegation Budgets** (Prevent excessive delegation):                                                    <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  - **Bias towards focused research** - Use single agent for simple questions, multiple only when clearly        <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  beneficial or when you have multiple independent research directions based on the user's request.              <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  - **Stop when adequate** - Don't over-research; stop when you have sufficient information                      <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  - **Limit iterations** - Stop after {max_researcher_iterations} task delegations if you haven't found          <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  adequate sources                                                                                               <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  <span style=\"color: #000080; text-decoration-color: #000080; font-weight: bold\">&lt;/Hard Limits&gt;</span>                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  <span style=\"color: #000080; text-decoration-color: #000080; font-weight: bold\">&lt;Scaling Rules&gt;</span>                                                                                                <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  **Simple fact-finding, lists, and rankings** can use a single sub-agent:                                       <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  - *Example*: \"List the top 10 coffee shops in San Francisco\" → Use 1 sub-agent, store in                       <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  `findings_coffee_shops.md`                                                                                     <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  **Comparisons** can use a sub-agent for each element of the comparison:                                        <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  - *Example*: \"Compare OpenAI vs. Anthropic vs. DeepMind approaches to AI safety\" → Use 3 sub-agents            <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  - Store findings in separate files: `findings_openai_safety.md`, `findings_anthropic_safety.md`,               <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  `findings_deepmind_safety.md`                                                                                  <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  **Multi-faceted research** can use parallel agents for different aspects:                                      <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  - *Example*: \"Research renewable energy: costs, environmental impact, and adoption rates\" → Use 3 sub-agents   <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  - Organize findings by aspect in separate files                                                                <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  **Important Reminders:**                                                                                       <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  - Each **task** call creates a dedicated research agent with isolated context                                  <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  - Sub-agents can't see each other's work - provide complete standalone instructions                            <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  - Use clear, specific language - avoid acronyms or abbreviations in task descriptions                          <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  <span style=\"color: #000080; text-decoration-color: #000080; font-weight: bold\">&lt;/Scaling Rules&gt;</span>                                                                                               <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</span>\n", "</pre>\n"], "text/plain": ["\u001b[34m╭─\u001b[0m\u001b[34m───────────────────────────────────────────────────\u001b[0m\u001b[34m \u001b[0m\u001b[1;32mPrompt\u001b[0m\u001b[34m \u001b[0m\u001b[34m────────────────────────────────────────────────────\u001b[0m\u001b[34m─╮\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  You can delegate tasks to sub-agents.                                                                          \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  \u001b[1;34m<Task>\u001b[0m                                                                                                         \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  Your role is to coordinate research by delegating specific research tasks to sub-agents.                       \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  \u001b[1;34m</Task>\u001b[0m                                                                                                        \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  \u001b[1;34m<Available Tools>\u001b[0m                                                                                              \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  1. **task(description, subagent_type)**: Delegate research tasks to specialized sub-agents                     \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m     - description: Clear, specific research question or task                                                    \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m     - subagent_type: Type of agent to use (e.g., \"research-agent\")                                              \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  2. **think_tool(reflection)**: Reflect on the results of each delegated task and plan next steps.              \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m     - reflection: Your detailed reflection on the results of the task and next steps.                           \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  **PARALLEL RESEARCH**: When you identify multiple independent research directions, make multiple **task**      \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  tool calls in a single response to enable parallel execution. Use at most {max_concurrent_research_units}      \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  parallel agents per iteration.                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  \u001b[1;34m</Available Tools>\u001b[0m                                                                                             \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  \u001b[1;34m<Hard Limits>\u001b[0m                                                                                                  \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  **Task Delegation Budgets** (Prevent excessive delegation):                                                    \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  - **Bias towards focused research** - Use single agent for simple questions, multiple only when clearly        \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  beneficial or when you have multiple independent research directions based on the user's request.              \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  - **Stop when adequate** - Don't over-research; stop when you have sufficient information                      \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  - **Limit iterations** - Stop after {max_researcher_iterations} task delegations if you haven't found          \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  adequate sources                                                                                               \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  \u001b[1;34m</Hard Limits>\u001b[0m                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  \u001b[1;34m<Scaling Rules>\u001b[0m                                                                                                \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  **Simple fact-finding, lists, and rankings** can use a single sub-agent:                                       \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  - *Example*: \"List the top 10 coffee shops in San Francisco\" → Use 1 sub-agent, store in                       \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  `findings_coffee_shops.md`                                                                                     \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  **Comparisons** can use a sub-agent for each element of the comparison:                                        \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  - *Example*: \"Compare OpenAI vs. Anthropic vs. DeepMind approaches to AI safety\" → Use 3 sub-agents            \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  - Store findings in separate files: `findings_openai_safety.md`, `findings_anthropic_safety.md`,               \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  `findings_deepmind_safety.md`                                                                                  \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  **Multi-faceted research** can use parallel agents for different aspects:                                      \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  - *Example*: \"Research renewable energy: costs, environmental impact, and adoption rates\" → Use 3 sub-agents   \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  - Organize findings by aspect in separate files                                                                \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  **Important Reminders:**                                                                                       \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  - Each **task** call creates a dedicated research agent with isolated context                                  \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  - Sub-agents can't see each other's work - provide complete standalone instructions                            \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  - Use clear, specific language - avoid acronyms or abbreviations in task descriptions                          \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  \u001b[1;34m</Scaling Rules>\u001b[0m                                                                                               \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from utils import show_prompt\n", "\n", "from deep_agents_from_scratch.prompts import SUBAGENT_USAGE_INSTRUCTIONS\n", "\n", "show_prompt(SUBAGENT_USAGE_INSTRUCTIONS)"]}, {"cell_type": "markdown", "id": "55323f85-09ea-4263-8b8d-7ccae998bd4a", "metadata": {}, "source": ["Let's now build a research system with a supervisor and sub-agents. This will just be a mock-up version with pre-defined search results to demonstrate how the pieces go together. In the next lesson, you will build a full-fledged research system."]}, {"cell_type": "code", "execution_count": 4, "id": "798a4497", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<IPython.core.display.Image object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from datetime import datetime\n", "\n", "from IPython.display import Image, display\n", "from langchain.chat_models import init_chat_model\n", "from langchain_core.tools import tool\n", "from langgraph.prebuilt import create_react_agent\n", "\n", "from deep_agents_from_scratch.prompts import SUBAGENT_USAGE_INSTRUCTIONS\n", "from deep_agents_from_scratch.state import DeepAgentState\n", "from deep_agents_from_scratch.task_tool import _create_task_tool\n", "\n", "# Limits\n", "max_concurrent_research_units = 3\n", "max_researcher_iterations = 3\n", "\n", "# Mock search result\n", "search_result = \"\"\"The Model Context Protocol (MCP) is an open standard protocol developed \n", "by Anthropic to enable seamless integration between AI models and external systems like \n", "tools, databases, and other services. It acts as a standardized communication layer, \n", "allowing AI models to access and utilize data from various sources in a consistent and \n", "efficient manner. Essentially, MCP simplifies the process of connecting AI assistants \n", "to external services by providing a unified language for data exchange. \"\"\"\n", "\n", "\n", "# Mock search tool\n", "@tool(parse_docstring=True)\n", "def web_search(\n", "    query: str,\n", "):\n", "    \"\"\"Search the web for information on a specific topic.\n", "\n", "    This tool performs web searches and returns relevant results\n", "    for the given query. Use this when you need to gather information from\n", "    the internet about any topic.\n", "\n", "    Args:\n", "        query: The search query string. Be specific and clear about what\n", "               information you're looking for.\n", "\n", "    Returns:\n", "        Search results from the search engine.\n", "\n", "    Example:\n", "        web_search(\"machine learning applications in healthcare\")\n", "    \"\"\"\n", "    return search_result\n", "\n", "\n", "# Add mock research instructions\n", "SIMPLE_RESEARCH_INSTRUCTIONS = \"\"\"You are a researcher. Research the topic provided to you. IMPORTANT: Just make a single call to the web_search tool and use the result provided by the tool to answer the provided topic.\"\"\"\n", "\n", "# Create research sub-agent\n", "research_sub_agent = {\n", "    \"name\": \"research-agent\",\n", "    \"description\": \"Delegate research to the sub-agent researcher. Only give this researcher one topic at a time.\",\n", "    \"prompt\": SIMPLE_RESEARCH_INSTRUCTIONS,\n", "    \"tools\": [\"web_search\"],\n", "}\n", "\n", "# Create agent using create_react_agent directly\n", "model = init_chat_model(model=\"anthropic:claude-sonnet-4-20250514\", temperature=0.0)\n", "\n", "# Tools for sub-agent\n", "sub_agent_tools = [web_search]\n", "\n", "# Create task tool to delegate tasks to sub-agents\n", "task_tool = _create_task_tool(\n", "    sub_agent_tools, [research_sub_agent], model, DeepAgentState\n", ")\n", "\n", "# Tools\n", "delegation_tools = [task_tool]\n", "\n", "# Create agent with system prompt\n", "agent = create_react_agent(\n", "    model,\n", "    delegation_tools,\n", "    prompt=SUBAGENT_USAGE_INSTRUCTIONS.format(\n", "        max_concurrent_research_units=max_concurrent_research_units,\n", "        max_researcher_iterations=max_researcher_iterations,\n", "        date=datetime.now().strftime(\"%a %b %-d, %Y\"),\n", "    ),\n", "    state_schema=DeepAgentState,\n", ")\n", "\n", "# Show the agent\n", "display(Image(agent.get_graph(xray=True).draw_mermaid_png()))"]}, {"cell_type": "code", "execution_count": 5, "id": "bf7c527c", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #000080; text-decoration-color: #000080\">╭─────────────────────────────────────────────────── 🧑 Human ────────────────────────────────────────────────────╮</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> Give me an overview of Model Context Protocol (MCP).                                                            <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</span>\n", "</pre>\n"], "text/plain": ["\u001b[34m╭─\u001b[0m\u001b[34m──────────────────────────────────────────────────\u001b[0m\u001b[34m 🧑 Human \u001b[0m\u001b[34m───────────────────────────────────────────────────\u001b[0m\u001b[34m─╮\u001b[0m\n", "\u001b[34m│\u001b[0m Give me an overview of Model Context Protocol (MCP).                                                            \u001b[34m│\u001b[0m\n", "\u001b[34m╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">╭───────────────────────────────────────────────────── 📝 AI ─────────────────────────────────────────────────────╮</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> I'll research the Model Context Protocol (MCP) for you to provide a comprehensive overview.                     <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>                                                                                                                 <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> 🔧 Tool Call: task                                                                                              <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>    Args: {                                                                                                      <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>   \"description\": \"Research the Model Context Protocol (MCP) and provide a comprehensive overview including:     <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> what it is, its purpose and goals, key features and capabilities, how it works technically, who developed it,   <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> current adoption status, and any notable implementations or use cases\",                                         <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>   \"subagent_type\": \"research-agent\"                                                                             <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> }                                                                                                               <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>    ID: toolu_01AVSqpRSyxTmYxeycHCWjHu                                                                           <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</span>\n", "</pre>\n"], "text/plain": ["\u001b[37m╭─\u001b[0m\u001b[37m────────────────────────────────────────────────────\u001b[0m\u001b[37m 📝 AI \u001b[0m\u001b[37m────────────────────────────────────────────────────\u001b[0m\u001b[37m─╮\u001b[0m\n", "\u001b[37m│\u001b[0m I'll research the Model Context Protocol (MCP) for you to provide a comprehensive overview.                     \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m                                                                                                                 \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m 🔧 Tool Call: task                                                                                              \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m    Args: {                                                                                                      \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m   \"description\": \"Research the Model Context Protocol (MCP) and provide a comprehensive overview including:     \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m what it is, its purpose and goals, key features and capabilities, how it works technically, who developed it,   \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m current adoption status, and any notable implementations or use cases\",                                         \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m   \"subagent_type\": \"research-agent\"                                                                             \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m }                                                                                                               \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m    ID: toolu_01AVSqpRSyxTmYxeycHCWjHu                                                                           \u001b[37m│\u001b[0m\n", "\u001b[37m╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #808000; text-decoration-color: #808000\">╭──────────────────────────────────────────────── 🔧 Tool Output ─────────────────────────────────────────────────╮</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> Based on my research, here's a comprehensive overview of the Model Context Protocol (MCP):                      <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>                                                                                                                 <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> ## What is the Model Context Protocol (MCP)?                                                                    <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>                                                                                                                 <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> The Model Context Protocol (MCP) is an open standard protocol that serves as a standardized communication layer <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> between AI models and external systems. It enables AI assistants to seamlessly integrate with various tools,    <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> databases, and services through a unified interface.                                                            <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>                                                                                                                 <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> ## Purpose and Goals                                                                                            <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>                                                                                                                 <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> The primary purposes of MCP include:                                                                            <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>                                                                                                                 <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> - **Standardization**: Creating a consistent way for AI models to communicate with external systems             <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> - **Simplification**: Reducing the complexity of integrating AI assistants with various tools and services      <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> - **Interoperability**: Enabling different AI systems to work with the same external resources using a common   <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> protocol                                                                                                        <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> - **Efficiency**: Streamlining data exchange between AI models and external services                            <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> - **Accessibility**: Making it easier for developers to connect AI models to their existing infrastructure      <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>                                                                                                                 <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> ## Key Features and Capabilities                                                                                <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>                                                                                                                 <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> - **Unified Communication Layer**: Provides a single, standardized way for AI models to interact with external  <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> systems                                                                                                         <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> - **Tool Integration**: Enables AI assistants to access and utilize various tools and services                  <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> - **Database Connectivity**: Allows AI models to query and interact with databases                              <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> - **Service Integration**: Facilitates connections to web services and APIs                                     <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> - **Data Exchange**: Supports consistent and efficient data transfer between AI models and external resources   <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> - **Open Standard**: Being an open protocol, it promotes widespread adoption and community development          <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>                                                                                                                 <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> ## Technical Implementation                                                                                     <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>                                                                                                                 <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> MCP works by:                                                                                                   <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> - Acting as an intermediary protocol that translates between AI model requests and external system responses    <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> - Providing a standardized format for data exchange                                                             <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> - Enabling AI models to make requests to external systems through a consistent interface                        <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> - Supporting various types of integrations including tools, databases, and web services                         <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>                                                                                                                 <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> ## Developer                                                                                                    <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>                                                                                                                 <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> The Model Context Protocol was developed by **Anthropic**, the AI safety company known for creating the Claude  <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> AI assistant. This aligns with Anthrop<PERSON>'s focus on building safe and beneficial AI systems.                    <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>                                                                                                                 <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> ## Current Adoption Status                                                                                      <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>                                                                                                                 <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> As an open standard protocol, MCP is designed to promote widespread adoption across the AI ecosystem. Being     <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> developed by Anthropic, it represents their effort to create industry standards that can benefit the broader AI <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> community, though specific adoption metrics would require more detailed industry analysis.                      <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>                                                                                                                 <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> ## Notable Implementations and Use Cases                                                                        <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>                                                                                                                 <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> The protocol is particularly valuable for:                                                                      <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> - **AI Assistant Integration**: Connecting AI models like <PERSON> to external tools and services                 <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> - **Enterprise Applications**: Enabling AI systems to work with existing business infrastructure                <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> - **Developer Tools**: Providing a standardized way to build AI-powered applications                            <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> - **Database Integration**: Allowing AI models to query and analyze data from various database systems          <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> - **API Connectivity**: Facilitating connections between AI models and web services                             <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>                                                                                                                 <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> The Model Context Protocol represents a significant step toward creating more interoperable and capable AI      <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> systems by standardizing how they communicate with the external world, ultimately making AI assistants more     <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> useful and easier to integrate into existing workflows and systems.                                             <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</span>\n", "</pre>\n"], "text/plain": ["\u001b[33m╭─\u001b[0m\u001b[33m───────────────────────────────────────────────\u001b[0m\u001b[33m 🔧 Tool Output \u001b[0m\u001b[33m────────────────────────────────────────────────\u001b[0m\u001b[33m─╮\u001b[0m\n", "\u001b[33m│\u001b[0m Based on my research, here's a comprehensive overview of the Model Context Protocol (MCP):                      \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m                                                                                                                 \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m ## What is the Model Context Protocol (MCP)?                                                                    \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m                                                                                                                 \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m The Model Context Protocol (MCP) is an open standard protocol that serves as a standardized communication layer \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m between AI models and external systems. It enables AI assistants to seamlessly integrate with various tools,    \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m databases, and services through a unified interface.                                                            \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m                                                                                                                 \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m ## Purpose and Goals                                                                                            \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m                                                                                                                 \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m The primary purposes of MCP include:                                                                            \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m                                                                                                                 \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m - **Standardization**: Creating a consistent way for AI models to communicate with external systems             \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m - **Simplification**: Reducing the complexity of integrating AI assistants with various tools and services      \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m - **Interoperability**: Enabling different AI systems to work with the same external resources using a common   \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m protocol                                                                                                        \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m - **Efficiency**: Streamlining data exchange between AI models and external services                            \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m - **Accessibility**: Making it easier for developers to connect AI models to their existing infrastructure      \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m                                                                                                                 \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m ## Key Features and Capabilities                                                                                \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m                                                                                                                 \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m - **Unified Communication Layer**: Provides a single, standardized way for AI models to interact with external  \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m systems                                                                                                         \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m - **Tool Integration**: Enables AI assistants to access and utilize various tools and services                  \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m - **Database Connectivity**: Allows AI models to query and interact with databases                              \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m - **Service Integration**: Facilitates connections to web services and APIs                                     \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m - **Data Exchange**: Supports consistent and efficient data transfer between AI models and external resources   \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m - **Open Standard**: Being an open protocol, it promotes widespread adoption and community development          \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m                                                                                                                 \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m ## Technical Implementation                                                                                     \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m                                                                                                                 \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m MCP works by:                                                                                                   \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m - Acting as an intermediary protocol that translates between AI model requests and external system responses    \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m - Providing a standardized format for data exchange                                                             \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m - Enabling AI models to make requests to external systems through a consistent interface                        \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m - Supporting various types of integrations including tools, databases, and web services                         \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m                                                                                                                 \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m ## <PERSON><PERSON><PERSON>                                                                                                    \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m                                                                                                                 \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m The Model Context Protocol was developed by **Anthropic**, the AI safety company known for creating the Claude  \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m AI assistant. This aligns with <PERSON><PERSON><PERSON>'s focus on building safe and beneficial AI systems.                    \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m                                                                                                                 \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m ## Current Adoption Status                                                                                      \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m                                                                                                                 \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m As an open standard protocol, MCP is designed to promote widespread adoption across the AI ecosystem. Being     \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m developed by Anthropic, it represents their effort to create industry standards that can benefit the broader AI \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m community, though specific adoption metrics would require more detailed industry analysis.                      \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m                                                                                                                 \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m ## Notable Implementations and Use Cases                                                                        \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m                                                                                                                 \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m The protocol is particularly valuable for:                                                                      \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m - **AI Assistant Integration**: Connecting AI models like Claude to external tools and services                 \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m - **Enterprise Applications**: Enabling AI systems to work with existing business infrastructure                \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m - **Developer Tools**: Providing a standardized way to build AI-powered applications                            \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m - **Database Integration**: Allowing AI models to query and analyze data from various database systems          \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m - **API Connectivity**: Facilitating connections between AI models and web services                             \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m                                                                                                                 \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m The Model Context Protocol represents a significant step toward creating more interoperable and capable AI      \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m systems by standardizing how they communicate with the external world, ultimately making AI assistants more     \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m useful and easier to integrate into existing workflows and systems.                                             \u001b[33m│\u001b[0m\n", "\u001b[33m╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">╭───────────────────────────────────────────────────── 📝 AI ─────────────────────────────────────────────────────╮</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> Based on my research, here's a comprehensive overview of the Model Context Protocol (MCP):                      <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>                                                                                                                 <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> ## What is the Model Context Protocol (MCP)?                                                                    <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>                                                                                                                 <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> The Model Context Protocol (MCP) is an open standard protocol developed by Anthropic that serves as a           <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> standardized communication layer between AI models and external systems. It enables AI assistants to seamlessly <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> integrate with various tools, databases, and services through a unified interface.                              <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>                                                                                                                 <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> ## Key Purpose and Goals                                                                                        <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>                                                                                                                 <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> MCP was created to address several critical needs in the AI ecosystem:                                          <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>                                                                                                                 <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> - **Standardization**: Establishing a consistent way for AI models to communicate with external systems         <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> - **Simplification**: Reducing the complexity of integrating AI assistants with various tools and services      <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> - **Interoperability**: Enabling different AI systems to work with the same external resources using a common   <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> protocol                                                                                                        <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> - **Efficiency**: Streamlining data exchange between AI models and external services                            <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>                                                                                                                 <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> ## Core Features and Capabilities                                                                               <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>                                                                                                                 <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> - **Unified Communication Layer**: Provides a single, standardized interface for AI-external system             <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> interactions                                                                                                    <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> - **Tool Integration**: Enables AI assistants to access and utilize various external tools and services         <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> - **Database Connectivity**: Allows AI models to query and interact with databases seamlessly                   <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> - **Service Integration**: Facilitates connections to web services and APIs                                     <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> - **Open Standard**: Promotes widespread adoption and community-driven development                              <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>                                                                                                                 <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> ## How It Works                                                                                                 <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>                                                                                                                 <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> MCP functions as an intermediary protocol that:                                                                 <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> - Translates between AI model requests and external system responses                                            <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> - Provides a standardized format for data exchange                                                              <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> - Enables AI models to make requests through a consistent interface                                             <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> - Supports various integration types including tools, databases, and web services                               <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>                                                                                                                 <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> ## Current Status and Impact                                                                                    <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>                                                                                                                 <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> As an open standard developed by Anthropic, MCP represents a significant step toward creating more              <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> interoperable AI systems. It's particularly valuable for:                                                       <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>                                                                                                                 <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> - **AI Assistant Integration**: Connecting models like <PERSON> to external tools                                 <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> - **Enterprise Applications**: Enabling AI systems to work with existing business infrastructure                <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> - **Developer Tools**: Providing standardized AI-powered application development                                <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> - **Database Integration**: Allowing AI models to query and analyze data from various sources                   <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>                                                                                                                 <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> The Model Context Protocol essentially makes AI assistants more useful and easier to integrate into existing    <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> workflows by standardizing how they communicate with the external world, promoting a more connected and capable <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> AI ecosystem.                                                                                                   <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</span>\n", "</pre>\n"], "text/plain": ["\u001b[37m╭─\u001b[0m\u001b[37m────────────────────────────────────────────────────\u001b[0m\u001b[37m 📝 AI \u001b[0m\u001b[37m────────────────────────────────────────────────────\u001b[0m\u001b[37m─╮\u001b[0m\n", "\u001b[37m│\u001b[0m Based on my research, here's a comprehensive overview of the Model Context Protocol (MCP):                      \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m                                                                                                                 \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m ## What is the Model Context Protocol (MCP)?                                                                    \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m                                                                                                                 \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m The Model Context Protocol (MCP) is an open standard protocol developed by Anthropic that serves as a           \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m standardized communication layer between AI models and external systems. It enables AI assistants to seamlessly \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m integrate with various tools, databases, and services through a unified interface.                              \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m                                                                                                                 \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m ## Key Purpose and Goals                                                                                        \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m                                                                                                                 \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m MCP was created to address several critical needs in the AI ecosystem:                                          \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m                                                                                                                 \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m - **Standardization**: Establishing a consistent way for AI models to communicate with external systems         \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m - **Simplification**: Reducing the complexity of integrating AI assistants with various tools and services      \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m - **Interoperability**: Enabling different AI systems to work with the same external resources using a common   \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m protocol                                                                                                        \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m - **Efficiency**: Streamlining data exchange between AI models and external services                            \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m                                                                                                                 \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m ## Core Features and Capabilities                                                                               \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m                                                                                                                 \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m - **Unified Communication Layer**: Provides a single, standardized interface for AI-external system             \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m interactions                                                                                                    \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m - **Tool Integration**: Enables AI assistants to access and utilize various external tools and services         \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m - **Database Connectivity**: Allows AI models to query and interact with databases seamlessly                   \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m - **Service Integration**: Facilitates connections to web services and APIs                                     \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m - **Open Standard**: Promotes widespread adoption and community-driven development                              \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m                                                                                                                 \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m ## How It Works                                                                                                 \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m                                                                                                                 \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m MCP functions as an intermediary protocol that:                                                                 \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m - Translates between AI model requests and external system responses                                            \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m - Provides a standardized format for data exchange                                                              \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m - Enables AI models to make requests through a consistent interface                                             \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m - Supports various integration types including tools, databases, and web services                               \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m                                                                                                                 \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m ## Current Status and Impact                                                                                    \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m                                                                                                                 \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m As an open standard developed by Anthropic, MCP represents a significant step toward creating more              \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m interoperable AI systems. It's particularly valuable for:                                                       \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m                                                                                                                 \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m - **AI Assistant Integration**: Connecting models like <PERSON> to external tools                                 \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m - **Enterprise Applications**: Enabling AI systems to work with existing business infrastructure                \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m - **Developer Tools**: Providing standardized AI-powered application development                                \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m - **Database Integration**: Allowing AI models to query and analyze data from various sources                   \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m                                                                                                                 \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m The Model Context Protocol essentially makes AI assistants more useful and easier to integrate into existing    \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m workflows by standardizing how they communicate with the external world, promoting a more connected and capable \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m AI ecosystem.                                                                                                   \u001b[37m│\u001b[0m\n", "\u001b[37m╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from utils import format_messages\n", "\n", "result = agent.invoke(\n", "    {\n", "        \"messages\": [\n", "            {\n", "                \"role\": \"user\",\n", "                \"content\": \"Give me an overview of Model Context Protocol (MCP).\",\n", "            }\n", "        ],\n", "    }\n", ")\n", "\n", "format_messages(result[\"messages\"])"]}, {"cell_type": "markdown", "id": "d758bd65", "metadata": {}, "source": ["Trace: \n", "https://smith.langchain.com/public/26cc1c2b-e785-4c6d-a2a7-c30a31875fc7/r\n", "<!-- https://smith.langchain.com/public/edc4e672-db9c-457a-953d-f62e7813591c/r -->"]}, {"cell_type": "markdown", "id": "da67ae27", "metadata": {}, "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.13"}}, "nbformat": 4, "nbformat_minor": 5}