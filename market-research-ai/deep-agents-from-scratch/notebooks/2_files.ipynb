{"cells": [{"cell_type": "code", "execution_count": 1, "id": "8aabb159", "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "from dotenv import load_dotenv\n", "\n", "load_dotenv(os.path.join(\"..\", \".env\"), override=True)\n", "\n", "%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "markdown", "id": "78a407e4", "metadata": {}, "source": ["## Context Offloading: Filesystem\n", "\n", "<img src=\"./assets/agent_header_files.png\" width=\"800\" style=\"display:block; margin-left:0;\">\n", "\n", "Agent context windows can grow rapidly during complex tasks—the average Manus task uses approximately 50 tool calls, creating substantial context accumulation. A powerful technique for managing this growth is **context offloading** through filesystem operations. Rather than storing all tool call observations and intermediate results directly in the context window, agents can strategically save information to files and [fetch it as-needed](https://blog.langchain.com/context-engineering-for-agents/), maintaining focus while preserving access to critical information.\n", "\n", "This approach has been successfully implemented by production systems like [Manus](https://manus.im/blog/Context-Engineering-for-AI-Agents-Lessons-from-Building-Manus) and [Hugging Face Open Deep Research](https://huggingface.co/blog/open-deep-research). Anthropic's [multi-agent research system](https://www.anthropic.com/engineering/multi-agent-research-system#:~:text=Subagent%20output%20to%20a%20filesystem%20to%20minimize%20the%20%E2%80%98game%20of%20telephone.%E2%80%99) provides another compelling example, where subagents store their work in external systems and pass lightweight references back to coordinators. This prevents the \"game of telephone\" effect—information degradation as it passes through multiple agents—while enabling fresh subagents to spawn with clean contexts and retrieve stored context like research plans from memory when needed.\n", "\n", "By writing token-heavy context to files in sandboxed environments, agents can effectively manage memory while maintaining the ability to retrieve detailed information when necessary. This pattern is particularly valuable for structured outputs like code, reports, or data visualizations where specialized prompts produce better results than filtering through general coordinators, and for long-running research tasks where intermediate results need preservation without constant attention."]}, {"cell_type": "markdown", "id": "c9854e27-f800-4563-9ed1-82837039cf97", "metadata": {}, "source": ["### File Tools\n", "\n", "Our implementation uses a virtual filesystem approach that mocks the traditional filesystem within LangGraph state. The core insight is to use a simple dictionary where keys represent mock file paths and values contain the file content. This approach provides short-term, thread-wise persistence that's ideal for maintaining context within a single agent conversation, though it's not suited for information that needs to persist across different conversation threads. The file operations utilize LangGraph's `Command` type to update the agent state, enabling tools to modify the virtual filesystem and maintain proper state management throughout the agent's execution.\n", "\n", "You’ll build three file tools—`ls`, `read_file`, and `write_file`—that operate on the virtual file system.\n", "\n", "**Usage:** \n", "- When the LLM has information in its context that it wants to persist, it writes it to a file with `write_file`; later, the same agent or a subagent can retrieve it with `read_file`.\n", "- A tool call can write data to a file and provide the filename in the tool call return message to the LLM. The LLM can later decide to read some or all of the content, or could apply another tool to process the data. \n", "- Use `ls` to list available files.\n", "\n", "The read/write tools expect newline-delimited plain text (parsed with `str.splitlines()`).\n", "\n", "\n", "The descriptions in the prompts below describe in detail how they operate:"]}, {"cell_type": "code", "execution_count": 2, "id": "abe282a0", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #000080; text-decoration-color: #000080\">╭──────────────────────────────────────────────────── </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">Prompt</span><span style=\"color: #000080; text-decoration-color: #000080\"> ─────────────────────────────────────────────────────╮</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  List all files in the virtual filesystem stored in agent state.                                                <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  Shows what files currently exist in agent memory. Use this to orient yourself before other file operations     <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  and maintain awareness of your file organization.                                                              <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  No parameters required - simply call ls() to see all available files.                                          <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</span>\n", "</pre>\n"], "text/plain": ["\u001b[34m╭─\u001b[0m\u001b[34m───────────────────────────────────────────────────\u001b[0m\u001b[34m \u001b[0m\u001b[1;32mPrompt\u001b[0m\u001b[34m \u001b[0m\u001b[34m────────────────────────────────────────────────────\u001b[0m\u001b[34m─╮\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  List all files in the virtual filesystem stored in agent state.                                                \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  Shows what files currently exist in agent memory. Use this to orient yourself before other file operations     \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  and maintain awareness of your file organization.                                                              \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  No parameters required - simply call ls() to see all available files.                                          \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from utils import show_prompt\n", "\n", "from deep_agents_from_scratch.prompts import (\n", "    LS_DESCRIPTION,\n", "    READ_FILE_DESCRIPTION,\n", "    WRITE_FILE_DESCRIPTION,\n", ")\n", "\n", "show_prompt(LS_DESCRIPTION)"]}, {"cell_type": "code", "execution_count": 3, "id": "8dc7bb26", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #000080; text-decoration-color: #000080\">╭──────────────────────────────────────────────────── </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">Prompt</span><span style=\"color: #000080; text-decoration-color: #000080\"> ─────────────────────────────────────────────────────╮</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  Read content from a file in the virtual filesystem with optional pagination.                                   <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  This tool returns file content with line numbers (like `cat -n`) and supports reading large files in chunks    <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  to avoid context overflow.                                                                                     <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  Parameters:                                                                                                    <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  - file_path (required): Path to the file you want to read                                                      <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  - offset (optional, default=0): Line number to start reading from                                              <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  - limit (optional, default=2000): Maximum number of lines to read                                              <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  Essential before making any edits to understand existing content. Always read a file before editing it.        <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</span>\n", "</pre>\n"], "text/plain": ["\u001b[34m╭─\u001b[0m\u001b[34m───────────────────────────────────────────────────\u001b[0m\u001b[34m \u001b[0m\u001b[1;32mPrompt\u001b[0m\u001b[34m \u001b[0m\u001b[34m────────────────────────────────────────────────────\u001b[0m\u001b[34m─╮\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  Read content from a file in the virtual filesystem with optional pagination.                                   \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  This tool returns file content with line numbers (like `cat -n`) and supports reading large files in chunks    \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  to avoid context overflow.                                                                                     \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  Parameters:                                                                                                    \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  - file_path (required): Path to the file you want to read                                                      \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  - offset (optional, default=0): Line number to start reading from                                              \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  - limit (optional, default=2000): Maximum number of lines to read                                              \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  Essential before making any edits to understand existing content. Always read a file before editing it.        \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["show_prompt(READ_FILE_DESCRIPTION)"]}, {"cell_type": "code", "execution_count": 4, "id": "2de27788", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #000080; text-decoration-color: #000080\">╭──────────────────────────────────────────────────── </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">Prompt</span><span style=\"color: #000080; text-decoration-color: #000080\"> ─────────────────────────────────────────────────────╮</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  Create a new file or completely overwrite an existing file in the virtual filesystem.                          <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  This tool creates new files or replaces entire file contents. Use for initial file creation or complete        <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  rewrites. Files are stored persistently in agent state.                                                        <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  Parameters:                                                                                                    <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  - file_path (required): Path where the file should be created/overwritten                                      <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  - content (required): The complete content to write to the file                                                <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  Important: This replaces the entire file content. Use edit_file for partial modifications.                     <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</span>\n", "</pre>\n"], "text/plain": ["\u001b[34m╭─\u001b[0m\u001b[34m───────────────────────────────────────────────────\u001b[0m\u001b[34m \u001b[0m\u001b[1;32mPrompt\u001b[0m\u001b[34m \u001b[0m\u001b[34m────────────────────────────────────────────────────\u001b[0m\u001b[34m─╮\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  Create a new file or completely overwrite an existing file in the virtual filesystem.                          \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  This tool creates new files or replaces entire file contents. Use for initial file creation or complete        \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  rewrites. Files are stored persistently in agent state.                                                        \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  Parameters:                                                                                                    \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  - file_path (required): Path where the file should be created/overwritten                                      \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  - content (required): The complete content to write to the file                                                \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  Important: This replaces the entire file content. Use edit_file for partial modifications.                     \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["show_prompt(WRITE_FILE_DESCRIPTION)"]}, {"cell_type": "markdown", "id": "6d581826", "metadata": {}, "source": ["Let's implement these functions below. There are two items worth noting. The first is the use of `@tool(description=PROMPT)`. Note that when `description=\"xyz\" is in the tool decorator, \"xyz\" is sent to the LLM and the docstring is suppressed. It is often more convenient to have lengthy descriptions in a separate prompts file. This provides space to explain both the operation of the tool and how it should be used in this application. The second item is the error messages. These messages are intended for the LLM vs a human user. In an agentic system, the LLM can use the information in error messages to retry the operation. "]}, {"cell_type": "code", "execution_count": 5, "id": "9b7f3968", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Overwriting ../src/deep_agents_from_scratch/file_tools.py\n"]}], "source": ["%%writefile ../src/deep_agents_from_scratch/file_tools.py\n", "\"\"\"Virtual file system tools for agent state management.\n", "\n", "This module provides tools for managing a virtual filesystem stored in agent state,\n", "enabling context offloading and information persistence across agent interactions.\n", "\"\"\"\n", "\n", "from typing import Annotated\n", "\n", "from langchain_core.messages import ToolMessage\n", "from langchain_core.tools import InjectedToolCallId, tool\n", "from langgraph.prebuilt import InjectedState\n", "from langgraph.types import Command\n", "\n", "from deep_agents_from_scratch.prompts import (\n", "    LS_DESCRIPTION,\n", "    READ_FILE_DESCRIPTION,\n", "    WRITE_FILE_DESCRIPTION,\n", ")\n", "from deep_agents_from_scratch.state import DeepAgentState\n", "\n", "\n", "@tool(description=LS_DESCRIPTION)\n", "def ls(state: Annotated[DeepAgentState, InjectedState]) -> list[str]:\n", "    \"\"\"List all files in the virtual filesystem.\"\"\"\n", "    return list(state.get(\"files\", {}).keys())\n", "\n", "\n", "@tool(description=READ_FILE_DESCRIPTION, parse_docstring=True)\n", "def read_file(\n", "    file_path: str,\n", "    state: Annotated[DeepAgentState, InjectedState],\n", "    offset: int = 0,\n", "    limit: int = 2000,\n", ") -> str:\n", "    \"\"\"Read file content from virtual filesystem with optional offset and limit.\n", "\n", "    Args:\n", "        file_path: Path to the file to read\n", "        state: Agent state containing virtual filesystem (injected in tool node)\n", "        offset: Line number to start reading from (default: 0)\n", "        limit: Maximum number of lines to read (default: 2000)\n", "\n", "    Returns:\n", "        Formatted file content with line numbers, or error message if file not found\n", "    \"\"\"\n", "    files = state.get(\"files\", {})\n", "    if file_path not in files:\n", "        return f\"Error: File '{file_path}' not found\"\n", "\n", "    content = files[file_path]\n", "    if not content:\n", "        return \"System reminder: File exists but has empty contents\"\n", "\n", "    lines = content.splitlines()\n", "    start_idx = offset\n", "    end_idx = min(start_idx + limit, len(lines))\n", "\n", "    if start_idx >= len(lines):\n", "        return f\"Error: Line offset {offset} exceeds file length ({len(lines)} lines)\"\n", "\n", "    result_lines = []\n", "    for i in range(start_idx, end_idx):\n", "        line_content = lines[i][:2000]  # Truncate long lines\n", "        result_lines.append(f\"{i + 1:6d}\\t{line_content}\")\n", "\n", "    return \"\\n\".join(result_lines)\n", "\n", "\n", "@tool(description=WRITE_FILE_DESCRIPTION, parse_docstring=True)\n", "def write_file(\n", "    file_path: str,\n", "    content: str,\n", "    state: Annotated[DeepAgentState, InjectedState],\n", "    tool_call_id: Annotated[str, InjectedToolCallId],\n", ") -> Command:\n", "    \"\"\"Write content to a file in the virtual filesystem.\n", "\n", "    Args:\n", "        file_path: Path where the file should be created/updated\n", "        content: Content to write to the file\n", "        state: Agent state containing virtual filesystem (injected in tool node)\n", "        tool_call_id: Tool call identifier for message response (injected in tool node)\n", "\n", "    Returns:\n", "        Command to update agent state with new file content\n", "    \"\"\"\n", "    files = state.get(\"files\", {})\n", "    files[file_path] = content\n", "    return Command(\n", "        update={\n", "            \"files\": files,\n", "            \"messages\": [\n", "                ToolMessage(f\"Updated file {file_path}\", tool_call_id=tool_call_id)\n", "            ],\n", "        }\n", "    )"]}, {"cell_type": "markdown", "id": "d557741d-20c4-434e-b565-0dfd2729f7af", "metadata": {}, "source": ["# Revisiting state and reducer \n", "In the previous notebook, we defined the file state and reducer, but did not describe them. Let's do that here.\n", "In `DeepAgentState`, `files` is defined as a dictionary with a key and value. As was mentioned above, the key is the filename and the value is the content of the file. `files` are added to state using the `file_reducer` when the `Command` in `write_files` is executed. In this reducer, `left` is the existing files in state and `right` is new values. The final statement allows the new values to overwrite old values: `{**left, **right}`, <PERSON> unpacks `left` first, then `right`. Any duplicate keys from `right` overwrite the earlier values from `left`.\n", "\n", "```python\n", "def file_reducer(left, right):\n", "    \"\"\"Merge two file dictionaries, with right side taking precedence.\n", "\n", "    Used as a reducer function for the files field in agent state,\n", "    allowing incremental updates to the virtual file system.\n", "\n", "    Args:\n", "        left: Left side dictionary (existing files)\n", "        right: Right side dictionary (new/updated files)\n", "\n", "    Returns:\n", "        Merged dictionary with right values overriding left values\n", "    \"\"\"\n", "    if left is None:\n", "        return right\n", "    elif right is None:\n", "        return left\n", "    else:\n", "        return {**left, **right}\n", "\n", "\n", "class DeepAgentState(AgentState):\n", "    \"\"\"Extended agent state that includes task tracking and virtual file system.\n", "\n", "    Inherits from LangGraph's AgentState and adds:\n", "    - todos: List of Todo items for task planning and progress tracking\n", "    - files: Virtual file system stored as dict mapping filenames to content\n", "    \"\"\"\n", "\n", "    todos: NotRequired[list[Todo]]\n", "    files: Annotated[NotRequired[dict[str, str]], file_reducer]\n", "```"]}, {"cell_type": "markdown", "id": "07a0a899", "metadata": {}, "source": ["We have a virtual filesystem and tools to work with it.  Let's build a simple research agent to try it out. \n", "The agent will store the user's request and then read it back before answering the user's question! \n", "\n", "Note that this simple approach is [very useful with long-running agent trajectories](https://www.anthropic.com/engineering/multi-agent-research-system#:~:text=Long%2Dhorizon%20conversation,across%20extended%20interactions.)! In this simple example, all information is easily kept in context, but for long-running agents, context content can be compressed or eliminated. Storing information prior to compression and retrieving it when it's needed is smart context engineering."]}, {"cell_type": "code", "execution_count": 6, "id": "b6a1ac50", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #000080; text-decoration-color: #000080\">╭──────────────────────────────────────────────────── </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">Prompt</span><span style=\"color: #000080; text-decoration-color: #000080\"> ─────────────────────────────────────────────────────╮</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  You have access to a virtual file system to help you retain and save context.                                  <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  <span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">## Workflow Process                                                                                          </span>  <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  1. **Orient**: Use ls() to see existing files before starting work                                             <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  2. **Save**: Use write_file() to store the user's request so that we can keep it for later                     <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  3. **Read**: Once you are satisfied with the collected sources, read the saved file and use it to ensure that  <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  you directly answer the user's question.                                                                       <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  ================================================================================                               <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  IMPORTANT: Just make a single call to the web_search tool and use the result provided by the tool to answer    <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  the user's question.                                                                                           <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</span>\n", "</pre>\n"], "text/plain": ["\u001b[34m╭─\u001b[0m\u001b[34m───────────────────────────────────────────────────\u001b[0m\u001b[34m \u001b[0m\u001b[1;32mPrompt\u001b[0m\u001b[34m \u001b[0m\u001b[34m────────────────────────────────────────────────────\u001b[0m\u001b[34m─╮\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  You have access to a virtual file system to help you retain and save context.                                  \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  \u001b[1;35m## Workflow Process                                                                                          \u001b[0m  \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  1. **Orient**: Use ls() to see existing files before starting work                                             \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  2. **Save**: Use write_file() to store the user's request so that we can keep it for later                     \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  3. **Read**: Once you are satisfied with the collected sources, read the saved file and use it to ensure that  \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  you directly answer the user's question.                                                                       \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  ================================================================================                               \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  IMPORTANT: Just make a single call to the web_search tool and use the result provided by the tool to answer    \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  the user's question.                                                                                           \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# File usage instructions\n", "FILE_USAGE_INSTRUCTIONS = \"\"\"You have access to a virtual file system to help you retain and save context.                                  \n", "                                                                                                                \n", "## Workflow Process                                                                                            \n", "1. **Orient**: Use ls() to see existing files before starting work                                             \n", "2. **Save**: Use write_file() to store the user's request so that we can keep it for later                     \n", "3. **Read**: Once you are satisfied with the collected sources, read the saved file and use it to ensure that you directly answer the user's question.\"\"\"\n", "\n", "# Add mock research instructions\n", "SIMPLE_RESEARCH_INSTRUCTIONS = \"\"\"IMPORTANT: Just make a single call to the web_search tool and use the result provided by the tool to answer the user's question.\"\"\"\n", "\n", "# Full prompt\n", "INSTRUCTIONS = (\n", "    FILE_USAGE_INSTRUCTIONS + \"\\n\\n\" + \"=\" * 80 + \"\\n\\n\" + SIMPLE_RESEARCH_INSTRUCTIONS\n", ")\n", "show_prompt(INSTRUCTIONS)"]}, {"cell_type": "code", "execution_count": 7, "id": "a94f78c4", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<IPython.core.display.Image object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from IPython.display import Image, display\n", "from langchain.chat_models import init_chat_model\n", "from langchain_core.tools import tool\n", "from langgraph.prebuilt import create_react_agent\n", "from utils import format_messages\n", "\n", "from deep_agents_from_scratch.file_tools import ls, read_file, write_file\n", "from deep_agents_from_scratch.state import DeepAgentState\n", "\n", "# Mock search result\n", "search_result = \"\"\"The Model Context Protocol (MCP) is an open standard protocol developed \n", "by Anthropic to enable seamless integration between AI models and external systems like \n", "tools, databases, and other services. It acts as a standardized communication layer, \n", "allowing AI models to access and utilize data from various sources in a consistent and \n", "efficient manner. Essentially, MCP simplifies the process of connecting AI assistants \n", "to external services by providing a unified language for data exchange. \"\"\"\n", "\n", "\n", "# Mock search tool\n", "@tool(parse_docstring=True)\n", "def web_search(\n", "    query: str,\n", "):\n", "    \"\"\"Search the web for information on a specific topic.\n", "\n", "    This tool performs web searches and returns relevant results\n", "    for the given query. Use this when you need to gather information from\n", "    the internet about any topic.\n", "\n", "    Args:\n", "        query: The search query string. Be specific and clear about what\n", "               information you're looking for.\n", "\n", "    Returns:\n", "        Search results from search engine.\n", "\n", "    Example:\n", "        web_search(\"machine learning applications in healthcare\")\n", "    \"\"\"\n", "    return search_result\n", "\n", "\n", "# Create agent using create_react_agent directly\n", "model = init_chat_model(model=\"anthropic:claude-sonnet-4-20250514\", temperature=0.0)\n", "tools = [ls, read_file, write_file, web_search]\n", "\n", "# Create agent with system prompt\n", "agent = create_react_agent(\n", "    model, tools, prompt=INSTRUCTIONS, state_schema=DeepAgentState\n", ")\n", "\n", "# Show the agent\n", "display(Image(agent.get_graph(xray=True).draw_mermaid_png()))"]}, {"cell_type": "markdown", "id": "7054ebcc", "metadata": {}, "source": ["Start the graph with no `files` in state and an user research request. "]}, {"cell_type": "code", "execution_count": 8, "id": "c3e90ac0", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #000080; text-decoration-color: #000080\">╭─────────────────────────────────────────────────── 🧑 Human ────────────────────────────────────────────────────╮</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> Give me an overview of Model Context Protocol (MCP).                                                            <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</span>\n", "</pre>\n"], "text/plain": ["\u001b[34m╭─\u001b[0m\u001b[34m──────────────────────────────────────────────────\u001b[0m\u001b[34m 🧑 Human \u001b[0m\u001b[34m───────────────────────────────────────────────────\u001b[0m\u001b[34m─╮\u001b[0m\n", "\u001b[34m│\u001b[0m Give me an overview of Model Context Protocol (MCP).                                                            \u001b[34m│\u001b[0m\n", "\u001b[34m╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">╭───────────────────────────────────────────────────── 📝 AI ─────────────────────────────────────────────────────╮</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> I'll help you get an overview of Model Context Protocol (MCP). Let me start by checking my current file system  <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> and then search for information about MCP.                                                                      <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>                                                                                                                 <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> 🔧 Tool Call: ls                                                                                                <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>    Args: {}                                                                                                     <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>    ID: toolu_01KNty5tCGQzaCqNF2ULRquC                                                                           <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</span>\n", "</pre>\n"], "text/plain": ["\u001b[37m╭─\u001b[0m\u001b[37m────────────────────────────────────────────────────\u001b[0m\u001b[37m 📝 AI \u001b[0m\u001b[37m────────────────────────────────────────────────────\u001b[0m\u001b[37m─╮\u001b[0m\n", "\u001b[37m│\u001b[0m I'll help you get an overview of Model Context Protocol (MCP). Let me start by checking my current file system  \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m and then search for information about MCP.                                                                      \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m                                                                                                                 \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m 🔧 Tool Call: ls                                                                                                \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m    Args: {}                                                                                                     \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m    ID: toolu_01KNty5tCGQzaCqNF2ULRquC                                                                           \u001b[37m│\u001b[0m\n", "\u001b[37m╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #808000; text-decoration-color: #808000\">╭──────────────────────────────────────────────── 🔧 Tool Output ─────────────────────────────────────────────────╮</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>                                                                                                                 <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</span>\n", "</pre>\n"], "text/plain": ["\u001b[33m╭─\u001b[0m\u001b[33m───────────────────────────────────────────────\u001b[0m\u001b[33m 🔧 Tool Output \u001b[0m\u001b[33m────────────────────────────────────────────────\u001b[0m\u001b[33m─╮\u001b[0m\n", "\u001b[33m│\u001b[0m                                                                                                                 \u001b[33m│\u001b[0m\n", "\u001b[33m╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">╭───────────────────────────────────────────────────── 📝 AI ─────────────────────────────────────────────────────╮</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>                                                                                                                 <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> 🔧 Tool Call: write_file                                                                                        <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>    Args: {                                                                                                      <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>   \"file_path\": \"user_request.txt\",                                                                              <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>   \"content\": \"User Request: Give me an overview of Model Context Protocol (MCP).\\n\\nThe user wants a            <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> comprehensive overview of Model Context Protocol, including what it is, its purpose, key features, and how it   <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> works.\"                                                                                                         <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> }                                                                                                               <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>    ID: toolu_01ShZJgsTyM9uWJJnvXttPpE                                                                           <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</span>\n", "</pre>\n"], "text/plain": ["\u001b[37m╭─\u001b[0m\u001b[37m────────────────────────────────────────────────────\u001b[0m\u001b[37m 📝 AI \u001b[0m\u001b[37m────────────────────────────────────────────────────\u001b[0m\u001b[37m─╮\u001b[0m\n", "\u001b[37m│\u001b[0m                                                                                                                 \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m 🔧 Tool Call: write_file                                                                                        \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m    Args: {                                                                                                      \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m   \"file_path\": \"user_request.txt\",                                                                              \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m   \"content\": \"User Request: Give me an overview of Model Context Protocol (MCP).\\n\\nThe user wants a            \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m comprehensive overview of Model Context Protocol, including what it is, its purpose, key features, and how it   \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m works.\"                                                                                                         \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m }                                                                                                               \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m    ID: toolu_01ShZJgsTyM9uWJJnvXttPpE                                                                           \u001b[37m│\u001b[0m\n", "\u001b[37m╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #808000; text-decoration-color: #808000\">╭──────────────────────────────────────────────── 🔧 Tool Output ─────────────────────────────────────────────────╮</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> Updated file user_request.txt                                                                                   <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</span>\n", "</pre>\n"], "text/plain": ["\u001b[33m╭─\u001b[0m\u001b[33m───────────────────────────────────────────────\u001b[0m\u001b[33m 🔧 Tool Output \u001b[0m\u001b[33m────────────────────────────────────────────────\u001b[0m\u001b[33m─╮\u001b[0m\n", "\u001b[33m│\u001b[0m Updated file user_request.txt                                                                                   \u001b[33m│\u001b[0m\n", "\u001b[33m╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">╭───────────────────────────────────────────────────── 📝 AI ─────────────────────────────────────────────────────╮</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>                                                                                                                 <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> 🔧 Tool Call: web_search                                                                                        <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>    Args: {                                                                                                      <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>   \"query\": \"Model Context Protocol MCP overview what is it purpose features\"                                    <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> }                                                                                                               <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>    ID: toolu_01P4V5oXaH7yb4J43oopprqk                                                                           <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</span>\n", "</pre>\n"], "text/plain": ["\u001b[37m╭─\u001b[0m\u001b[37m────────────────────────────────────────────────────\u001b[0m\u001b[37m 📝 AI \u001b[0m\u001b[37m────────────────────────────────────────────────────\u001b[0m\u001b[37m─╮\u001b[0m\n", "\u001b[37m│\u001b[0m                                                                                                                 \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m 🔧 Tool Call: web_search                                                                                        \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m    Args: {                                                                                                      \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m   \"query\": \"Model Context Protocol MCP overview what is it purpose features\"                                    \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m }                                                                                                               \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m    ID: toolu_01P4V5oXaH7yb4J43oopprqk                                                                           \u001b[37m│\u001b[0m\n", "\u001b[37m╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #808000; text-decoration-color: #808000\">╭──────────────────────────────────────────────── 🔧 Tool Output ─────────────────────────────────────────────────╮</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> The Model Context Protocol (MCP) is an open standard protocol developed                                         <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> by Anthropic to enable seamless integration between AI models and external systems like                         <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> tools, databases, and other services. It acts as a standardized communication layer,                            <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> allowing AI models to access and utilize data from various sources in a consistent and                          <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> efficient manner. Essentially, MCP simplifies the process of connecting AI assistants                           <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> to external services by providing a unified language for data exchange.                                         <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</span>\n", "</pre>\n"], "text/plain": ["\u001b[33m╭─\u001b[0m\u001b[33m───────────────────────────────────────────────\u001b[0m\u001b[33m 🔧 Tool Output \u001b[0m\u001b[33m────────────────────────────────────────────────\u001b[0m\u001b[33m─╮\u001b[0m\n", "\u001b[33m│\u001b[0m The Model Context Protocol (MCP) is an open standard protocol developed                                         \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m by <PERSON><PERSON><PERSON> to enable seamless integration between AI models and external systems like                         \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m tools, databases, and other services. It acts as a standardized communication layer,                            \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m allowing AI models to access and utilize data from various sources in a consistent and                          \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m efficient manner. Essentially, MCP simplifies the process of connecting AI assistants                           \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m to external services by providing a unified language for data exchange.                                         \u001b[33m│\u001b[0m\n", "\u001b[33m╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">╭───────────────────────────────────────────────────── 📝 AI ─────────────────────────────────────────────────────╮</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>                                                                                                                 <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> 🔧 Tool Call: read_file                                                                                         <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>    Args: {                                                                                                      <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>   \"file_path\": \"user_request.txt\"                                                                               <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> }                                                                                                               <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>    ID: toolu_01UHFjiMbrboijJivWgNDSG3                                                                           <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</span>\n", "</pre>\n"], "text/plain": ["\u001b[37m╭─\u001b[0m\u001b[37m────────────────────────────────────────────────────\u001b[0m\u001b[37m 📝 AI \u001b[0m\u001b[37m────────────────────────────────────────────────────\u001b[0m\u001b[37m─╮\u001b[0m\n", "\u001b[37m│\u001b[0m                                                                                                                 \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m 🔧 Tool Call: read_file                                                                                         \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m    Args: {                                                                                                      \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m   \"file_path\": \"user_request.txt\"                                                                               \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m }                                                                                                               \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m    ID: toolu_01UHFjiMbrboijJivWgNDSG3                                                                           \u001b[37m│\u001b[0m\n", "\u001b[37m╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #808000; text-decoration-color: #808000\">╭──────────────────────────────────────────────── 🔧 Tool Output ─────────────────────────────────────────────────╮</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>      1  User Request: Give me an overview of Model Context Protocol (MCP).                                      <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>      2                                                                                                          <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>      3  The user wants a comprehensive overview of Model Context Protocol, including what it is, its purpose,   <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> key features, and how it works.                                                                                 <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</span>\n", "</pre>\n"], "text/plain": ["\u001b[33m╭─\u001b[0m\u001b[33m───────────────────────────────────────────────\u001b[0m\u001b[33m 🔧 Tool Output \u001b[0m\u001b[33m────────────────────────────────────────────────\u001b[0m\u001b[33m─╮\u001b[0m\n", "\u001b[33m│\u001b[0m      1  User Request: Give me an overview of Model Context Protocol (MCP).                                      \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m      2                                                                                                          \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m      3  The user wants a comprehensive overview of Model Context Protocol, including what it is, its purpose,   \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m key features, and how it works.                                                                                 \u001b[33m│\u001b[0m\n", "\u001b[33m╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">╭───────────────────────────────────────────────────── 📝 AI ─────────────────────────────────────────────────────╮</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> Based on my search, here's a comprehensive overview of Model Context Protocol (MCP):                            <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>                                                                                                                 <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> ## What is Model Context Protocol (MCP)?                                                                        <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>                                                                                                                 <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> Model Context Protocol (MCP) is an **open standard protocol** developed by Anthropic that serves as a           <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> standardized communication layer between AI models and external systems. It's designed to enable seamless       <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> integration and data exchange between AI assistants and various external services.                              <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>                                                                                                                 <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> ## Key Purpose                                                                                                  <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>                                                                                                                 <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> MCP addresses a fundamental challenge in AI development: **connecting AI models to external data sources and    <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> tools**. Instead of building custom integrations for each service, MCP provides a unified framework that allows <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> AI models to:                                                                                                   <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>                                                                                                                 <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> - Access databases                                                                                              <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> - Interact with APIs                                                                                            <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> - Use external tools and services                                                                               <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> - Retrieve information from various data sources                                                                <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>                                                                                                                 <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> ## How It Works                                                                                                 <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>                                                                                                                 <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> MCP acts as a **bridge or translation layer** that:                                                             <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>                                                                                                                 <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> 1. **Standardizes Communication**: Provides a consistent language and protocol for data exchange                <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> 2. **Simplifies Integration**: Eliminates the need for custom connectors for each external service              <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> 3. **Enables Interoperability**: Allows different AI systems to work with the same external resources using the <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> same protocol                                                                                                   <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>                                                                                                                 <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> ## Key Benefits                                                                                                 <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>                                                                                                                 <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> - **Consistency**: One protocol to rule them all - no need to learn different APIs for each service             <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> - **Efficiency**: Streamlined data access and tool usage for AI models                                          <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> - **Scalability**: Easy to add new services and tools without rebuilding integrations                           <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> - **Open Standard**: Being open-source, it promotes widespread adoption and community development               <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>                                                                                                                 <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> ## Significance                                                                                                 <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>                                                                                                                 <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> MCP represents an important step toward making AI assistants more capable and versatile by giving them          <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> standardized access to external resources. This could significantly enhance the practical utility of AI systems <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> by allowing them to work with real-world data and tools in a more seamless manner.                              <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>                                                                                                                 <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> The protocol essentially aims to solve the \"last mile\" problem of AI integration - making it easier for AI      <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> models to actually interact with and utilize the vast ecosystem of digital tools and data sources that exist in <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> modern computing environments.                                                                                  <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</span>\n", "</pre>\n"], "text/plain": ["\u001b[37m╭─\u001b[0m\u001b[37m────────────────────────────────────────────────────\u001b[0m\u001b[37m 📝 AI \u001b[0m\u001b[37m────────────────────────────────────────────────────\u001b[0m\u001b[37m─╮\u001b[0m\n", "\u001b[37m│\u001b[0m Based on my search, here's a comprehensive overview of Model Context Protocol (MCP):                            \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m                                                                                                                 \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m ## What is Model Context Protocol (MCP)?                                                                        \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m                                                                                                                 \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m Model Context Protocol (MCP) is an **open standard protocol** developed by Anthropic that serves as a           \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m standardized communication layer between AI models and external systems. It's designed to enable seamless       \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m integration and data exchange between AI assistants and various external services.                              \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m                                                                                                                 \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m ## Key Purpose                                                                                                  \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m                                                                                                                 \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m MCP addresses a fundamental challenge in AI development: **connecting AI models to external data sources and    \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m tools**. Instead of building custom integrations for each service, MCP provides a unified framework that allows \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m AI models to:                                                                                                   \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m                                                                                                                 \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m - Access databases                                                                                              \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m - Interact with APIs                                                                                            \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m - Use external tools and services                                                                               \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m - Retrieve information from various data sources                                                                \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m                                                                                                                 \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m ## How It Works                                                                                                 \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m                                                                                                                 \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m MCP acts as a **bridge or translation layer** that:                                                             \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m                                                                                                                 \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m 1. **Standardizes Communication**: Provides a consistent language and protocol for data exchange                \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m 2. **Simplifies Integration**: Eliminates the need for custom connectors for each external service              \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m 3. **Enables Interoperability**: Allows different AI systems to work with the same external resources using the \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m same protocol                                                                                                   \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m                                                                                                                 \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m ## Key Benefits                                                                                                 \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m                                                                                                                 \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m - **Consistency**: One protocol to rule them all - no need to learn different APIs for each service             \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m - **Efficiency**: Streamlined data access and tool usage for AI models                                          \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m - **Scalability**: Easy to add new services and tools without rebuilding integrations                           \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m - **Open Standard**: Being open-source, it promotes widespread adoption and community development               \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m                                                                                                                 \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m ## Significance                                                                                                 \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m                                                                                                                 \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m MCP represents an important step toward making AI assistants more capable and versatile by giving them          \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m standardized access to external resources. This could significantly enhance the practical utility of AI systems \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m by allowing them to work with real-world data and tools in a more seamless manner.                              \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m                                                                                                                 \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m The protocol essentially aims to solve the \"last mile\" problem of AI integration - making it easier for AI      \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m models to actually interact with and utilize the vast ecosystem of digital tools and data sources that exist in \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m modern computing environments.                                                                                  \u001b[37m│\u001b[0m\n", "\u001b[37m╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["result = agent.invoke(\n", "    {\n", "        \"messages\": [\n", "            {\n", "                \"role\": \"user\",\n", "                \"content\": \"Give me an overview of Model Context Protocol (MCP).\",\n", "            }\n", "        ],\n", "        \"files\": {},\n", "    }\n", ")\n", "format_messages(result[\"messages\"])"]}, {"cell_type": "markdown", "id": "4addd08d", "metadata": {}, "source": ["We can see the file saved in our mock file system. "]}, {"cell_type": "code", "execution_count": 9, "id": "449c8ecd", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'user_request.txt': 'User Request: Give me an overview of Model Context Protocol (MCP).\\n\\nThe user wants a comprehensive overview of Model Context Protocol, including what it is, its purpose, key features, and how it works.'}"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["result[\"files\"]"]}, {"cell_type": "markdown", "id": "bbbb506b", "metadata": {}, "source": ["Trace: \n", "https://smith.langchain.com/public/b03e20b4-e908-488d-84a9-8f891d17addd/r\n", "<!-- https://smith.langchain.com/public/1066102f-d7b3-423c-b556-006865a1d479/r -->"]}, {"cell_type": "code", "execution_count": null, "id": "a2f4ec9b-6dd5-43cf-a0ee-274b0b638c01", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}