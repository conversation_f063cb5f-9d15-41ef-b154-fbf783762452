{"cells": [{"cell_type": "code", "execution_count": 1, "id": "fecc3e39", "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "from dotenv import load_dotenv\n", "\n", "load_dotenv(os.path.join(\"..\", \".env\"), override=True)\n", "\n", "%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "markdown", "id": "7ba3ee5b", "metadata": {}, "source": ["# Deep Agent for Research\n", "\n", "## Overview \n", "\n", "<img src=\"./assets/agent_header.png\" width=\"800\" style=\"display:block; margin-left:0;\">\n", "\n", "Now, we can put everything we have learned together:\n", "\n", "* We will use **TODOs** to keep track of tasks. \n", "* We will use **files** to store raw tool call results. \n", "* We will **delegate research tasks to sub-agents** for context isolation. \n", "\n", "## Search Tool \n", "\n", "We'll build a search tool that offloads raw contents to files and returns only a summary to the agent. This is a common pattern for long-running agent trajectories, [as we've seen with <PERSON><PERSON>](https://manus.im/blog/Context-Engineering-for-AI-Agents-Lessons-from-Building-Manus)! \n", "\n", "### Core Components\n", "\n", "1. **Search Execution (`run_tavily_search`)**: Performs the actual web search using Tavily API with configurable parameters for results count and topic filtering.\n", "\n", "2. **Content Summarization (`summarize_webpage_content`)**: Uses a lightweight model (GPT-4o-mini) to generate structured summaries of webpage content, producing both a descriptive filename and key learnings summary.\n", "\n", "3. **Result Processing (`process_search_results`)**: Fetches full webpage content via HTTP, converts HTML to markdown using `markdownify`, and generates summaries for each result.\n", "\n", "4. **Context Offloading (`tavily_search` tool)**: The main tool that:\n", "   - Executes search and processes results\n", "   - Saves full content to files in agent state (context offloading)\n", "   - Returns only minimal summaries to the agent (prevents context spam)\n", "   - Uses LangGraph `Command` to update both files and messages\n", "\n", "5. **Strategic Thinking (`think_tool`)**: Provides a structured reflection mechanism for agents to analyze findings, assess gaps, and plan next steps in their research workflow.\n", "\n", "This architecture solves the token efficiency problem by storing detailed search results in files while keeping the agent's working context minimal and focused."]}, {"cell_type": "code", "execution_count": 2, "id": "e8bae934", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Overwriting ../src/deep_agents_from_scratch/research_tools.py\n"]}], "source": ["%%writefile ../src/deep_agents_from_scratch/research_tools.py\n", "\"\"\"Research Tools.\n", "\n", "This module provides search and content processing utilities for the research agent,\n", "including web search capabilities and content summarization tools.\n", "\"\"\"\n", "import os\n", "from datetime import datetime\n", "import uuid, base64\n", "\n", "import httpx\n", "from langchain.chat_models import init_chat_model\n", "from langchain_core.messages import HumanMessage, ToolMessage\n", "from langchain_core.tools import InjectedToolArg, InjectedToolCallId, tool\n", "from langgraph.prebuilt import InjectedState\n", "from langgraph.types import Command\n", "from markdownify import markdownify\n", "from pydantic import BaseModel, Field\n", "from tavily import TavilyClient\n", "from typing_extensions import Annotated, Literal\n", "\n", "from deep_agents_from_scratch.prompts import SUMMARIZE_WEB_SEARCH\n", "from deep_agents_from_scratch.state import DeepAgentState\n", "\n", "# Summarization model \n", "summarization_model = init_chat_model(model=\"openai:gpt-4o-mini\")\n", "tavily_client = TavilyClient()\n", "\n", "class Summary(BaseModel):\n", "    \"\"\"Schema for webpage content summarization.\"\"\"\n", "    filename: str = Field(description=\"Name of the file to store.\")\n", "    summary: str = Field(description=\"Key learnings from the webpage.\")\n", "\n", "def get_today_str() -> str:\n", "    \"\"\"Get current date in a human-readable format.\"\"\"\n", "    return datetime.now().strftime(\"%a %b %-d, %Y\")\n", "\n", "def run_tavily_search(\n", "    search_query: str, \n", "    max_results: int = 1, \n", "    topic: Literal[\"general\", \"news\", \"finance\"] = \"general\", \n", "    include_raw_content: bool = True, \n", ") -> dict:\n", "    \"\"\"Perform search using Tavily API for a single query.\n", "\n", "    Args:\n", "        search_query: Search query to execute\n", "        max_results: Maximum number of results per query\n", "        topic: Topic filter for search results\n", "        include_raw_content: Whether to include raw webpage content\n", "\n", "    Returns:\n", "        Search results dictionary\n", "    \"\"\"\n", "    result = tavily_client.search(\n", "        search_query,\n", "        max_results=max_results,\n", "        include_raw_content=include_raw_content,\n", "        topic=topic\n", "    )\n", "\n", "    return result\n", "\n", "def summarize_webpage_content(webpage_content: str) -> Summary:\n", "    \"\"\"Summarize webpage content using the configured summarization model.\n", "    \n", "    Args:\n", "        webpage_content: Raw webpage content to summarize\n", "        \n", "    Returns:\n", "        Summary object with filename and summary\n", "    \"\"\"\n", "    try:\n", "        # Set up structured output model for summarization\n", "        structured_model = summarization_model.with_structured_output(Summary)\n", "        \n", "        # Generate summary\n", "        summary_and_filename = structured_model.invoke([\n", "            HumanMessage(content=SUMMARIZE_WEB_SEARCH.format(\n", "                webpage_content=webpage_content, \n", "                date=get_today_str()\n", "            ))\n", "        ])\n", "        \n", "        return summary_and_filename\n", "        \n", "    except Exception:\n", "        # Return a basic summary object on failure\n", "        return Summary(\n", "            filename=\"search_result.md\",\n", "            summary=webpage_content[:1000] + \"...\" if len(webpage_content) > 1000 else webpage_content\n", "        )\n", "\n", "def process_search_results(results: dict) -> list[dict]:\n", "    \"\"\"Process search results by summarizing content where available.\n", "    \n", "    Args:\n", "        results: Tavily search results dictionary\n", "        \n", "    Returns:\n", "        List of processed results with summaries\n", "    \"\"\"\n", "    processed_results = []\n", "\n", "    # Create a client for HTTP requests\n", "    HTTPX_CLIENT = httpx.Client()\n", "    \n", "    for result in results.get('results', []):\n", "        \n", "        # Get url \n", "        url = result['url']\n", "        \n", "        # Read url\n", "        response = HTTPX_CLIENT.get(url)\n", "\n", "        if response.status_code == 200:\n", "            # Convert HTML to markdown\n", "            raw_content = markdownify(response.text)\n", "            summary_obj = summarize_webpage_content(raw_content)\n", "        else:\n", "            # Use <PERSON><PERSON>'s generated summary\n", "            raw_content = result.get('raw_content', '')\n", "            summary_obj = Summary(\n", "                filename=\"URL_error.md\",\n", "                summary=result.get('content', 'Error reading URL; try another search.')\n", "            )\n", "        \n", "        # uniquify file names\n", "        uid = base64.urlsafe_b64encode(uuid.uuid4().bytes).rstrip(b\"=\").decode(\"ascii\")[:8]\n", "        name, ext = os.path.splitext(summary_obj.filename)\n", "        summary_obj.filename = f\"{name}_{uid}{ext}\"\n", "\n", "        processed_results.append({\n", "            'url': result['url'],\n", "            'title': result['title'],\n", "            'summary': summary_obj.summary,\n", "            'filename': summary_obj.filename,\n", "            'raw_content': raw_content,\n", "        })\n", "    \n", "    return processed_results\n", "\n", "@tool(parse_docstring=True)\n", "def tavily_search(\n", "    query: str,\n", "    state: Annotated[DeepAgentState, InjectedState],\n", "    tool_call_id: Annotated[str, InjectedToolCallId],\n", "    max_results: Annotated[int, InjectedToolArg] = 1,\n", "    topic: Annotated[Literal[\"general\", \"news\", \"finance\"], InjectedToolArg] = \"general\",\n", ") -> Command:\n", "    \"\"\"Search web and save detailed results to files while returning minimal context.\n", "\n", "    Performs web search and saves full content to files for context offloading.\n", "    Returns only essential information to help the agent decide on next steps.\n", "\n", "    Args:\n", "        query: Search query to execute\n", "        state: Injected agent state for file storage\n", "        tool_call_id: Injected tool call identifier\n", "        max_results: Maximum number of results to return (default: 1)\n", "        topic: Topic filter - 'general', 'news', or 'finance' (default: 'general')\n", "\n", "    Returns:\n", "        Command that saves full results to files and provides minimal summary\n", "    \"\"\"\n", "    # Execute search\n", "    search_results = run_tavily_search(\n", "        query,\n", "        max_results=max_results,\n", "        topic=topic,\n", "        include_raw_content=True,\n", "    ) \n", "\n", "    # Process and summarize results\n", "    processed_results = process_search_results(search_results)\n", "    \n", "    # Save each result to a file and prepare summary\n", "    files = state.get(\"files\", {})\n", "    saved_files = []\n", "    summaries = []\n", "    \n", "    for i, result in enumerate(processed_results):\n", "        # Use the AI-generated filename from summarization\n", "        filename = result['filename']\n", "        \n", "        # Create file content with full details\n", "        file_content = f\"\"\"# Search Result: {result['title']}\n", "\n", "**URL:** {result['url']}\n", "**Query:** {query}\n", "**Date:** {get_today_str()}\n", "\n", "## Summary\n", "{result['summary']}\n", "\n", "## Raw Content\n", "{result['raw_content'] if result['raw_content'] else 'No raw content available'}\n", "\"\"\"\n", "        \n", "        files[filename] = file_content\n", "        saved_files.append(filename)\n", "        summaries.append(f\"- {filename}: {result['summary']}...\")\n", "    \n", "    # Create minimal summary for tool message - focus on what was collected\n", "    summary_text = f\"\"\"🔍 Found {len(processed_results)} result(s) for '{query}':\n", "\n", "{chr(10).join(summaries)}\n", "\n", "Files: {', '.join(saved_files)}\n", "💡 Use read_file() to access full details when needed.\"\"\"\n", "\n", "    return Command(\n", "        update={\n", "            \"files\": files,\n", "            \"messages\": [\n", "                ToolMessage(summary_text, tool_call_id=tool_call_id)\n", "            ],\n", "        }\n", "    )\n", "\n", "@tool(parse_docstring=True)\n", "def think_tool(reflection: str) -> str:\n", "    \"\"\"Tool for strategic reflection on research progress and decision-making.\n", "\n", "    Use this tool after each search to analyze results and plan next steps systematically.\n", "    This creates a deliberate pause in the research workflow for quality decision-making.\n", "\n", "    When to use:\n", "    - After receiving search results: What key information did I find?\n", "    - Before deciding next steps: Do I have enough to answer comprehensively?\n", "    - When assessing research gaps: What specific information am I still missing?\n", "    - Before concluding research: Can I provide a complete answer now?\n", "    - How complex is the question: Have I reached the number of search limits?\n", "\n", "    Reflection should address:\n", "    1. Analysis of current findings - What concrete information have I gathered?\n", "    2. Gap assessment - What crucial information is still missing?\n", "    3. Quality evaluation - Do I have sufficient evidence/examples for a good answer?\n", "    4. Strategic decision - Should I continue searching or provide my answer?\n", "\n", "    Args:\n", "        reflection: Your detailed reflection on research progress, findings, gaps, and next steps\n", "\n", "    Returns:\n", "        Confirmation that reflection was recorded for decision-making\n", "    \"\"\"\n", "    return f\"Reflection recorded: {reflection}\""]}, {"cell_type": "markdown", "id": "abd74759", "metadata": {}, "source": ["## Deep Agent\n", "\n", "Now, we can just apply all of our prior learnings: \n", "\n", "* We'll give the researcher a `think_tool` and our `search_tool` above.\n", "* We'll give our parent agent file tools, a `think_tool`, and a `task` tool. "]}, {"cell_type": "code", "execution_count": 3, "id": "4487f04d", "metadata": {}, "outputs": [], "source": ["from datetime import datetime\n", "\n", "from IPython.display import Image, display\n", "from langchain.chat_models import init_chat_model\n", "from langgraph.prebuilt import create_react_agent\n", "from utils import show_prompt, stream_agent\n", "\n", "from deep_agents_from_scratch.file_tools import ls, read_file, write_file\n", "from deep_agents_from_scratch.prompts import (\n", "    FILE_USAGE_INSTRUCTIONS,\n", "    RESEARCHER_INSTRUCTIONS,\n", "    SUBAGENT_USAGE_INSTRUCTIONS,\n", "    TODO_USAGE_INSTRUCTIONS,\n", ")\n", "from deep_agents_from_scratch.research_tools import tavily_search, think_tool, get_today_str\n", "from deep_agents_from_scratch.state import DeepAgentState\n", "from deep_agents_from_scratch.task_tool import _create_task_tool\n", "from deep_agents_from_scratch.todo_tools import write_todos, read_todos\n", "\n", "# Create agent using create_react_agent directly\n", "model = init_chat_model(model=\"anthropic:claude-sonnet-4-20250514\", temperature=0.0)\n", "\n", "# Limits\n", "max_concurrent_research_units = 3\n", "max_researcher_iterations = 3\n", "\n", "# Tools\n", "sub_agent_tools = [tavily_search, think_tool]\n", "built_in_tools = [ls, read_file, write_file, write_todos, read_todos, think_tool]\n", "\n", "# Create research sub-agent\n", "research_sub_agent = {\n", "    \"name\": \"research-agent\",\n", "    \"description\": \"Delegate research to the sub-agent researcher. Only give this researcher one topic at a time.\",\n", "    \"prompt\": RESEARCHER_INSTRUCTIONS.format(date=get_today_str()),\n", "    \"tools\": [\"tavily_search\", \"think_tool\"],\n", "}\n", "\n", "# Create task tool to delegate tasks to sub-agents\n", "task_tool = _create_task_tool(\n", "    sub_agent_tools, [research_sub_agent], model, DeepAgentState\n", ")\n", "\n", "delegation_tools = [task_tool]\n", "all_tools = sub_agent_tools + built_in_tools + delegation_tools  # search available to main agent for trivial cases\n", "\n", "# Build prompt\n", "SUBAGENT_INSTRUCTIONS = SUBAGENT_USAGE_INSTRUCTIONS.format(\n", "    max_concurrent_research_units=max_concurrent_research_units,\n", "    max_researcher_iterations=max_researcher_iterations,\n", "    date=datetime.now().strftime(\"%a %b %-d, %Y\"),\n", ")"]}, {"cell_type": "code", "execution_count": 4, "id": "a5eb7a89-8a26-4fb4-ba77-b05180f2c67e", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #000080; text-decoration-color: #000080\">╭──────────────────────────────────────────────────── </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">Prompt</span><span style=\"color: #000080; text-decoration-color: #000080\"> ─────────────────────────────────────────────────────╮</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  You are a research assistant conducting research on the user's input topic. For context, today's date is       <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  {date}.                                                                                                        <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  <span style=\"color: #000080; text-decoration-color: #000080; font-weight: bold\">&lt;Task&gt;</span>                                                                                                         <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  Your job is to use tools to gather information about the user's input topic.                                   <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  You can use any of the tools provided to you to find resources that can help answer the research question.     <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  You can call these tools in series or in parallel, your research is conducted in a tool-calling loop.          <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  <span style=\"color: #000080; text-decoration-color: #000080; font-weight: bold\">&lt;/Task&gt;</span>                                                                                                        <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  <span style=\"color: #000080; text-decoration-color: #000080; font-weight: bold\">&lt;Available Tools&gt;</span>                                                                                              <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  You have access to two main tools:                                                                             <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  1. **tavily_search**: For conducting web searches to gather information                                        <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  2. **think_tool**: For reflection and strategic planning during research                                       <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  **CRITICAL: Use think_tool after each search to reflect on results and plan next steps**                       <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  <span style=\"color: #000080; text-decoration-color: #000080; font-weight: bold\">&lt;/Available Tools&gt;</span>                                                                                             <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  <span style=\"color: #000080; text-decoration-color: #000080; font-weight: bold\">&lt;Instructions&gt;</span>                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  Think like a human researcher with limited time. Follow these steps:                                           <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  1. **Read the question carefully** - What specific information does the user need?                             <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  2. **Start with broader searches** - Use broad, comprehensive queries first                                    <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  3. **After each search, pause and assess** - Do I have enough to answer? What's still missing?                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  4. **Execute narrower searches as you gather information** - Fill in the gaps                                  <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  5. **Stop when you can answer confidently** - Don't keep searching for perfection                              <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  <span style=\"color: #000080; text-decoration-color: #000080; font-weight: bold\">&lt;/Instructions&gt;</span>                                                                                                <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  <span style=\"color: #000080; text-decoration-color: #000080; font-weight: bold\">&lt;Hard Limits&gt;</span>                                                                                                  <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  **Tool Call Budgets** (Prevent excessive searching):                                                           <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  - **Simple queries**: Use 1-2 search tool calls maximum                                                        <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  - **Normal queries**: Use 2-3 search tool calls maximum                                                        <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  - **Very Complex queries**: Use up to 5 search tool calls maximum                                              <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  - **Always stop**: After 5 search tool calls if you cannot find the right sources                              <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  **Stop Immediately When**:                                                                                     <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  - You can answer the user's question comprehensively                                                           <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  - You have 3+ relevant examples/sources for the question                                                       <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  - Your last 2 searches returned similar information                                                            <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  <span style=\"color: #000080; text-decoration-color: #000080; font-weight: bold\">&lt;/Hard Limits&gt;</span>                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  <span style=\"color: #000080; text-decoration-color: #000080; font-weight: bold\">&lt;Show Your Thinking&gt;</span>                                                                                           <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  After each search tool call, use think_tool to analyze the results:                                            <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  - What key information did I find?                                                                             <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  - What's missing?                                                                                              <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  - Do I have enough to answer the question comprehensively?                                                     <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  - Should I search more or provide my answer?                                                                   <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  <span style=\"color: #000080; text-decoration-color: #000080; font-weight: bold\">&lt;/Show Your Thinking&gt;</span>                                                                                          <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</span>\n", "</pre>\n"], "text/plain": ["\u001b[34m╭─\u001b[0m\u001b[34m───────────────────────────────────────────────────\u001b[0m\u001b[34m \u001b[0m\u001b[1;32mPrompt\u001b[0m\u001b[34m \u001b[0m\u001b[34m────────────────────────────────────────────────────\u001b[0m\u001b[34m─╮\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  You are a research assistant conducting research on the user's input topic. For context, today's date is       \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  {date}.                                                                                                        \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  \u001b[1;34m<Task>\u001b[0m                                                                                                         \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  Your job is to use tools to gather information about the user's input topic.                                   \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  You can use any of the tools provided to you to find resources that can help answer the research question.     \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  You can call these tools in series or in parallel, your research is conducted in a tool-calling loop.          \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  \u001b[1;34m</Task>\u001b[0m                                                                                                        \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  \u001b[1;34m<Available Tools>\u001b[0m                                                                                              \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  You have access to two main tools:                                                                             \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  1. **tavily_search**: For conducting web searches to gather information                                        \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  2. **think_tool**: For reflection and strategic planning during research                                       \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  **CRITICAL: Use think_tool after each search to reflect on results and plan next steps**                       \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  \u001b[1;34m</Available Tools>\u001b[0m                                                                                             \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  \u001b[1;34m<Instructions>\u001b[0m                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  Think like a human researcher with limited time. Follow these steps:                                           \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  1. **Read the question carefully** - What specific information does the user need?                             \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  2. **Start with broader searches** - Use broad, comprehensive queries first                                    \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  3. **After each search, pause and assess** - Do I have enough to answer? What's still missing?                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  4. **Execute narrower searches as you gather information** - Fill in the gaps                                  \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  5. **Stop when you can answer confidently** - Don't keep searching for perfection                              \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  \u001b[1;34m</Instructions>\u001b[0m                                                                                                \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  \u001b[1;34m<Hard Limits>\u001b[0m                                                                                                  \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  **Tool Call Budgets** (Prevent excessive searching):                                                           \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  - **Simple queries**: Use 1-2 search tool calls maximum                                                        \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  - **Normal queries**: Use 2-3 search tool calls maximum                                                        \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  - **Very Complex queries**: Use up to 5 search tool calls maximum                                              \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  - **Always stop**: After 5 search tool calls if you cannot find the right sources                              \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  **Stop Immediately When**:                                                                                     \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  - You can answer the user's question comprehensively                                                           \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  - You have 3+ relevant examples/sources for the question                                                       \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  - Your last 2 searches returned similar information                                                            \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  \u001b[1;34m</Hard Limits>\u001b[0m                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  \u001b[1;34m<Show Your Thinking>\u001b[0m                                                                                           \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  After each search tool call, use think_tool to analyze the results:                                            \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  - What key information did I find?                                                                             \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  - What's missing?                                                                                              \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  - Do I have enough to answer the question comprehensively?                                                     \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  - Should I search more or provide my answer?                                                                   \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  \u001b[1;34m</Show Your Thinking>\u001b[0m                                                                                          \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["show_prompt(RESEARCHER_INSTRUCTIONS)"]}, {"cell_type": "code", "execution_count": 5, "id": "475d752f", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #000080; text-decoration-color: #000080\">╭──────────────────────────────────────────────────── </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">Prompt</span><span style=\"color: #000080; text-decoration-color: #000080\"> ─────────────────────────────────────────────────────╮</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  # TODO MANAGEMENT                                                                                              <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  Based upon the user's request:                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  1. Use the write_todos tool to create TODO at the start of a user request, per the tool description.           <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  2. After you accomplish a TODO, use the read_todos to read the TODOs in order to remind yourself of the plan.  <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  3. Reflect on what you've done and the TODO.                                                                   <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  4. Mark you task as completed, and proceed to the next TODO.                                                   <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  5. Continue this process until you have completed all TODOs.                                                   <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  IMPORTANT: Always create a research plan of TODOs and conduct research following the above guidelines for ANY  <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  user request.                                                                                                  <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  IMPORTANT: Aim to batch research tasks into a *single TODO* in order to minimize the number of TODOs you have  <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  to keep track of.                                                                                              <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  ================================================================================                               <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  # FILE SYSTEM USAGE                                                                                            <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  You have access to a virtual file system to help you retain and save context.                                  <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  <span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">## Workflow Process</span>                                                                                            <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  1. **Orient**: Use ls() to see existing files before starting work                                             <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  2. **Save**: Use write_file() to store the user's request so that we can keep it for later                     <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  3. **Research**: Proceed with research. The search tool will write files.                                      <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  4. **Read**: Once you are satisfied with the collected sources, read the files and use them to answer the      <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  user's question directly.                                                                                      <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  ================================================================================                               <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  # SUB-AGENT DELEGATION                                                                                         <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  You can delegate tasks to sub-agents.                                                                          <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  <span style=\"color: #000080; text-decoration-color: #000080; font-weight: bold\">&lt;Task&gt;</span>                                                                                                         <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  Your role is to coordinate research by delegating specific research tasks to sub-agents.                       <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  <span style=\"color: #000080; text-decoration-color: #000080; font-weight: bold\">&lt;/Task&gt;</span>                                                                                                        <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  <span style=\"color: #000080; text-decoration-color: #000080; font-weight: bold\">&lt;Available Tools&gt;</span>                                                                                              <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  1. **task(description, subagent_type)**: Delegate research tasks to specialized sub-agents                     <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>     - description: Clear, specific research question or task                                                    <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>     - subagent_type: Type of agent to use (e.g., \"research-agent\")                                              <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  2. **think_tool(reflection)**: Reflect on the results of each delegated task and plan next steps.              <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>     - reflection: Your detailed reflection on the results of the task and next steps.                           <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  **PARALLEL RESEARCH**: When you identify multiple independent research directions, make multiple **task**      <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  tool calls in a single response to enable parallel execution. Use at most 3 parallel agents per iteration.     <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  <span style=\"color: #000080; text-decoration-color: #000080; font-weight: bold\">&lt;/Available Tools&gt;</span>                                                                                             <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  <span style=\"color: #000080; text-decoration-color: #000080; font-weight: bold\">&lt;Hard Limits&gt;</span>                                                                                                  <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  **Task Delegation Budgets** (Prevent excessive delegation):                                                    <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  - **Bias towards focused research** - Use single agent for simple questions, multiple only when clearly        <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  beneficial or when you have multiple independent research directions based on the user's request.              <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  - **Stop when adequate** - Don't over-research; stop when you have sufficient information                      <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  - **Limit iterations** - Stop after 3 task delegations if you haven't found adequate sources                   <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  <span style=\"color: #000080; text-decoration-color: #000080; font-weight: bold\">&lt;/Hard Limits&gt;</span>                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  <span style=\"color: #000080; text-decoration-color: #000080; font-weight: bold\">&lt;Scaling Rules&gt;</span>                                                                                                <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  **Simple fact-finding, lists, and rankings** can use a single sub-agent:                                       <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  - *Example*: \"List the top 10 coffee shops in San Francisco\" → Use 1 sub-agent, store in                       <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  `findings_coffee_shops.md`                                                                                     <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  **Comparisons** can use a sub-agent for each element of the comparison:                                        <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  - *Example*: \"Compare OpenAI vs. Anthropic vs. DeepMind approaches to AI safety\" → Use 3 sub-agents            <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  - Store findings in separate files: `findings_openai_safety.md`, `findings_anthropic_safety.md`,               <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  `findings_deepmind_safety.md`                                                                                  <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  **Multi-faceted research** can use parallel agents for different aspects:                                      <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  - *Example*: \"Research renewable energy: costs, environmental impact, and adoption rates\" → Use 3 sub-agents   <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  - Organize findings by aspect in separate files                                                                <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  **Important Reminders:**                                                                                       <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  - Each **task** call creates a dedicated research agent with isolated context                                  <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  - Sub-agents can't see each other's work - provide complete standalone instructions                            <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  - Use clear, specific language - avoid acronyms or abbreviations in task descriptions                          <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  <span style=\"color: #000080; text-decoration-color: #000080; font-weight: bold\">&lt;/Scaling Rules&gt;</span>                                                                                               <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</span>\n", "</pre>\n"], "text/plain": ["\u001b[34m╭─\u001b[0m\u001b[34m───────────────────────────────────────────────────\u001b[0m\u001b[34m \u001b[0m\u001b[1;32mPrompt\u001b[0m\u001b[34m \u001b[0m\u001b[34m────────────────────────────────────────────────────\u001b[0m\u001b[34m─╮\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  # TODO MANAGEMENT                                                                                              \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  Based upon the user's request:                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  1. Use the write_todos tool to create TODO at the start of a user request, per the tool description.           \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  2. After you accomplish a TODO, use the read_todos to read the TODOs in order to remind yourself of the plan.  \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  3. Reflect on what you've done and the TODO.                                                                   \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  4. Mark you task as completed, and proceed to the next TODO.                                                   \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  5. Continue this process until you have completed all TODOs.                                                   \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  IMPORTANT: Always create a research plan of TODOs and conduct research following the above guidelines for ANY  \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  user request.                                                                                                  \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  IMPORTANT: Aim to batch research tasks into a *single TODO* in order to minimize the number of TODOs you have  \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  to keep track of.                                                                                              \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  ================================================================================                               \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  # FILE SYSTEM USAGE                                                                                            \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  You have access to a virtual file system to help you retain and save context.                                  \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  \u001b[1;35m## Workflow Process\u001b[0m                                                                                            \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  1. **Orient**: Use ls() to see existing files before starting work                                             \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  2. **Save**: Use write_file() to store the user's request so that we can keep it for later                     \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  3. **Research**: Proceed with research. The search tool will write files.                                      \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  4. **Read**: Once you are satisfied with the collected sources, read the files and use them to answer the      \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  user's question directly.                                                                                      \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  ================================================================================                               \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  # SUB-AGENT DELEGATION                                                                                         \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  You can delegate tasks to sub-agents.                                                                          \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  \u001b[1;34m<Task>\u001b[0m                                                                                                         \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  Your role is to coordinate research by delegating specific research tasks to sub-agents.                       \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  \u001b[1;34m</Task>\u001b[0m                                                                                                        \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  \u001b[1;34m<Available Tools>\u001b[0m                                                                                              \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  1. **task(description, subagent_type)**: Delegate research tasks to specialized sub-agents                     \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m     - description: Clear, specific research question or task                                                    \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m     - subagent_type: Type of agent to use (e.g., \"research-agent\")                                              \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  2. **think_tool(reflection)**: Reflect on the results of each delegated task and plan next steps.              \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m     - reflection: Your detailed reflection on the results of the task and next steps.                           \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  **PARALLEL RESEARCH**: When you identify multiple independent research directions, make multiple **task**      \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  tool calls in a single response to enable parallel execution. Use at most 3 parallel agents per iteration.     \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  \u001b[1;34m</Available Tools>\u001b[0m                                                                                             \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  \u001b[1;34m<Hard Limits>\u001b[0m                                                                                                  \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  **Task Delegation Budgets** (Prevent excessive delegation):                                                    \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  - **Bias towards focused research** - Use single agent for simple questions, multiple only when clearly        \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  beneficial or when you have multiple independent research directions based on the user's request.              \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  - **Stop when adequate** - Don't over-research; stop when you have sufficient information                      \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  - **Limit iterations** - Stop after 3 task delegations if you haven't found adequate sources                   \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  \u001b[1;34m</Hard Limits>\u001b[0m                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  \u001b[1;34m<Scaling Rules>\u001b[0m                                                                                                \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  **Simple fact-finding, lists, and rankings** can use a single sub-agent:                                       \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  - *Example*: \"List the top 10 coffee shops in San Francisco\" → Use 1 sub-agent, store in                       \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  `findings_coffee_shops.md`                                                                                     \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  **Comparisons** can use a sub-agent for each element of the comparison:                                        \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  - *Example*: \"Compare OpenAI vs. Anthropic vs. DeepMind approaches to AI safety\" → Use 3 sub-agents            \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  - Store findings in separate files: `findings_openai_safety.md`, `findings_anthropic_safety.md`,               \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  `findings_deepmind_safety.md`                                                                                  \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  **Multi-faceted research** can use parallel agents for different aspects:                                      \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  - *Example*: \"Research renewable energy: costs, environmental impact, and adoption rates\" → Use 3 sub-agents   \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  - Organize findings by aspect in separate files                                                                \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  **Important Reminders:**                                                                                       \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  - Each **task** call creates a dedicated research agent with isolated context                                  \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  - Sub-agents can't see each other's work - provide complete standalone instructions                            \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  - Use clear, specific language - avoid acronyms or abbreviations in task descriptions                          \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  \u001b[1;34m</Scaling Rules>\u001b[0m                                                                                               \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["INSTRUCTIONS = (\n", "    \"# TODO MANAGEMENT\\n\"\n", "    + TODO_USAGE_INSTRUCTIONS\n", "    + \"\\n\\n\"\n", "    + \"=\" * 80\n", "    + \"\\n\\n\"\n", "    + \"# FILE SYSTEM USAGE\\n\"\n", "    + FILE_USAGE_INSTRUCTIONS\n", "    + \"\\n\\n\"\n", "    + \"=\" * 80\n", "    + \"\\n\\n\"\n", "    + \"# SUB-AGENT DELEGATION\\n\"\n", "    + SUBAGENT_INSTRUCTIONS\n", ")\n", "\n", "show_prompt(INSTRUCTIONS)"]}, {"cell_type": "code", "execution_count": 6, "id": "29a13c5f", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<IPython.core.display.Image object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Create agent\n", "agent = create_react_agent(\n", "    model, all_tools, prompt=INSTRUCTIONS, state_schema=DeepAgentState\n", ")\n", "\n", "# Show the agent\n", "display(Image(agent.get_graph(xray=True).draw_mermaid_png()))"]}, {"cell_type": "code", "execution_count": 7, "id": "e042e655", "metadata": {"scrolled": true}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #000080; text-decoration-color: #000080\">╭─────────────────────────────────────────────────── 🧑 Human ────────────────────────────────────────────────────╮</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> Give me an overview of Model Context Protocol (MCP).                                                            <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</span>\n", "</pre>\n"], "text/plain": ["\u001b[34m╭─\u001b[0m\u001b[34m──────────────────────────────────────────────────\u001b[0m\u001b[34m 🧑 Human \u001b[0m\u001b[34m───────────────────────────────────────────────────\u001b[0m\u001b[34m─╮\u001b[0m\n", "\u001b[34m│\u001b[0m Give me an overview of Model Context Protocol (MCP).                                                            \u001b[34m│\u001b[0m\n", "\u001b[34m╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">╭───────────────────────────────────────────────────── 📝 AI ─────────────────────────────────────────────────────╮</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> I'll help you get a comprehensive overview of Model Context Protocol (MCP). Let me start by creating a research <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> plan and then gathering the necessary information.                                                              <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>                                                                                                                 <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> 🔧 Tool Call: ls                                                                                                <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>    Args: {}                                                                                                     <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>    ID: toolu_015o1KvXqkpHuYFFuqDwdb7C                                                                           <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</span>\n", "</pre>\n"], "text/plain": ["\u001b[37m╭─\u001b[0m\u001b[37m────────────────────────────────────────────────────\u001b[0m\u001b[37m 📝 AI \u001b[0m\u001b[37m────────────────────────────────────────────────────\u001b[0m\u001b[37m─╮\u001b[0m\n", "\u001b[37m│\u001b[0m I'll help you get a comprehensive overview of Model Context Protocol (MCP). Let me start by creating a research \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m plan and then gathering the necessary information.                                                              \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m                                                                                                                 \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m 🔧 Tool Call: ls                                                                                                \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m    Args: {}                                                                                                     \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m    ID: toolu_015o1KvXqkpHuYFFuqDwdb7C                                                                           \u001b[37m│\u001b[0m\n", "\u001b[37m╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #808000; text-decoration-color: #808000\">╭──────────────────────────────────────────────── 🔧 Tool Output ─────────────────────────────────────────────────╮</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>                                                                                                                 <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</span>\n", "</pre>\n"], "text/plain": ["\u001b[33m╭─\u001b[0m\u001b[33m───────────────────────────────────────────────\u001b[0m\u001b[33m 🔧 Tool Output \u001b[0m\u001b[33m────────────────────────────────────────────────\u001b[0m\u001b[33m─╮\u001b[0m\n", "\u001b[33m│\u001b[0m                                                                                                                 \u001b[33m│\u001b[0m\n", "\u001b[33m╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">╭───────────────────────────────────────────────────── 📝 AI ─────────────────────────────────────────────────────╮</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>                                                                                                                 <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> 🔧 Tool Call: write_file                                                                                        <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>    Args: {                                                                                                      <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>   \"file_path\": \"user_request.md\",                                                                               <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>   \"content\": \"# User Request\\n\\nGive me an overview of Model Context Protocol (MCP).\\n\\n## Research             <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> Goal\\nProvide a comprehensive overview of Model Context Protocol including:\\n- What it is and its purpose\\n-    <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> Key features and capabilities\\n- How it works\\n- Use cases and applications\\n- Current status and adoption\"     <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> }                                                                                                               <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>    ID: toolu_01U2eHiMBKbrsuixKUTNrCmF                                                                           <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</span>\n", "</pre>\n"], "text/plain": ["\u001b[37m╭─\u001b[0m\u001b[37m────────────────────────────────────────────────────\u001b[0m\u001b[37m 📝 AI \u001b[0m\u001b[37m────────────────────────────────────────────────────\u001b[0m\u001b[37m─╮\u001b[0m\n", "\u001b[37m│\u001b[0m                                                                                                                 \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m 🔧 Tool Call: write_file                                                                                        \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m    Args: {                                                                                                      \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m   \"file_path\": \"user_request.md\",                                                                               \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m   \"content\": \"# User Request\\n\\nGive me an overview of Model Context Protocol (MCP).\\n\\n## Research             \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m Goal\\nProvide a comprehensive overview of Model Context Protocol including:\\n- What it is and its purpose\\n-    \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m Key features and capabilities\\n- How it works\\n- Use cases and applications\\n- Current status and adoption\"     \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m }                                                                                                               \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m    ID: toolu_01U2eHiMBKbrsuixKUTNrCmF                                                                           \u001b[37m│\u001b[0m\n", "\u001b[37m╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #808000; text-decoration-color: #808000\">╭──────────────────────────────────────────────── 🔧 Tool Output ─────────────────────────────────────────────────╮</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> Updated file user_request.md                                                                                    <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</span>\n", "</pre>\n"], "text/plain": ["\u001b[33m╭─\u001b[0m\u001b[33m───────────────────────────────────────────────\u001b[0m\u001b[33m 🔧 Tool Output \u001b[0m\u001b[33m────────────────────────────────────────────────\u001b[0m\u001b[33m─╮\u001b[0m\n", "\u001b[33m│\u001b[0m Updated file user_request.md                                                                                    \u001b[33m│\u001b[0m\n", "\u001b[33m╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">╭───────────────────────────────────────────────────── 📝 AI ─────────────────────────────────────────────────────╮</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>                                                                                                                 <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> 🔧 Tool Call: write_todos                                                                                       <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>    Args: {                                                                                                      <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>   \"todos\": [                                                                                                    <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>     {                                                                                                           <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>       \"content\": \"Research Model Context Protocol (MCP) - definition, purpose, features, architecture, use      <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> cases, and current status\",                                                                                     <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>       \"status\": \"pending\"                                                                                       <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>     },                                                                                                          <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>     {                                                                                                           <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>       \"content\": \"Synthesize findings into comprehensive overview for the user\",                                <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>       \"status\": \"pending\"                                                                                       <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>     }                                                                                                           <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>   ]                                                                                                             <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> }                                                                                                               <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>    ID: toolu_013e56bxC4guFR5UYpKmeMPq                                                                           <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</span>\n", "</pre>\n"], "text/plain": ["\u001b[37m╭─\u001b[0m\u001b[37m────────────────────────────────────────────────────\u001b[0m\u001b[37m 📝 AI \u001b[0m\u001b[37m────────────────────────────────────────────────────\u001b[0m\u001b[37m─╮\u001b[0m\n", "\u001b[37m│\u001b[0m                                                                                                                 \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m 🔧 Tool Call: write_todos                                                                                       \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m    Args: {                                                                                                      \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m   \"todos\": [                                                                                                    \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m     {                                                                                                           \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m       \"content\": \"Research Model Context Protocol (MCP) - definition, purpose, features, architecture, use      \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m cases, and current status\",                                                                                     \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m       \"status\": \"pending\"                                                                                       \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m     },                                                                                                          \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m     {                                                                                                           \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m       \"content\": \"Synthesize findings into comprehensive overview for the user\",                                \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m       \"status\": \"pending\"                                                                                       \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m     }                                                                                                           \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m   ]                                                                                                             \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m }                                                                                                               \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m    ID: toolu_013e56bxC4guFR5UYpKmeMPq                                                                           \u001b[37m│\u001b[0m\n", "\u001b[37m╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #808000; text-decoration-color: #808000\">╭──────────────────────────────────────────────── 🔧 Tool Output ─────────────────────────────────────────────────╮</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> Updated todo list to [{'content': 'Research Model Context Protocol (MCP) - definition, purpose, features,       <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> architecture, use cases, and current status', 'status': 'pending'}, {'content': 'Synthesize findings into       <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> comprehensive overview for the user', 'status': 'pending'}]                                                     <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</span>\n", "</pre>\n"], "text/plain": ["\u001b[33m╭─\u001b[0m\u001b[33m───────────────────────────────────────────────\u001b[0m\u001b[33m 🔧 Tool Output \u001b[0m\u001b[33m────────────────────────────────────────────────\u001b[0m\u001b[33m─╮\u001b[0m\n", "\u001b[33m│\u001b[0m Updated todo list to [{'content': 'Research Model Context Protocol (MCP) - definition, purpose, features,       \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m architecture, use cases, and current status', 'status': 'pending'}, {'content': 'Synthesize findings into       \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m comprehensive overview for the user', 'status': 'pending'}]                                                     \u001b[33m│\u001b[0m\n", "\u001b[33m╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">╭───────────────────────────────────────────────────── 📝 AI ─────────────────────────────────────────────────────╮</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> Now I'll delegate the research task to gather comprehensive information about Model Context Protocol.           <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>                                                                                                                 <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> 🔧 Tool Call: task                                                                                              <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>    Args: {                                                                                                      <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>   \"description\": \"Research Model Context Protocol (MCP) comprehensively. I need detailed information about: 1)  <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> What MCP is and its core purpose/goals, 2) Key technical features and capabilities, 3) How the protocol works   <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> architecturally, 4) Main use cases and applications, 5) Current development status, adoption, and who's behind  <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> it, 6) Any notable implementations or examples. Focus on official sources and technical documentation.\",        <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>   \"subagent_type\": \"research-agent\"                                                                             <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> }                                                                                                               <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>    ID: toolu_01FQLakdXJKpQqCCjoR6VLqJ                                                                           <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</span>\n", "</pre>\n"], "text/plain": ["\u001b[37m╭─\u001b[0m\u001b[37m────────────────────────────────────────────────────\u001b[0m\u001b[37m 📝 AI \u001b[0m\u001b[37m────────────────────────────────────────────────────\u001b[0m\u001b[37m─╮\u001b[0m\n", "\u001b[37m│\u001b[0m Now I'll delegate the research task to gather comprehensive information about Model Context Protocol.           \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m                                                                                                                 \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m 🔧 Tool Call: task                                                                                              \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m    Args: {                                                                                                      \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m   \"description\": \"Research Model Context Protocol (MCP) comprehensively. I need detailed information about: 1)  \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m What MCP is and its core purpose/goals, 2) Key technical features and capabilities, 3) How the protocol works   \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m architecturally, 4) Main use cases and applications, 5) Current development status, adoption, and who's behind  \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m it, 6) Any notable implementations or examples. Focus on official sources and technical documentation.\",        \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m   \"subagent_type\": \"research-agent\"                                                                             \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m }                                                                                                               \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m    ID: toolu_01FQLakdXJKpQqCCjoR6VLqJ                                                                           \u001b[37m│\u001b[0m\n", "\u001b[37m╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #808000; text-decoration-color: #808000\">╭──────────────────────────────────────────────── 🔧 Tool Output ─────────────────────────────────────────────────╮</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> Based on my research of official documentation and technical sources, here's a comprehensive overview of the    <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> Model Context Protocol (MCP):                                                                                   <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>                                                                                                                 <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> ## 1. What MCP Is and Its Core Purpose/Goals                                                                    <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>                                                                                                                 <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> The Model Context Protocol (MCP) is an **open protocol designed for seamless integration between large language <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> model (LLM) applications and external data sources and tools**. Its core purpose is to standardize how AI       <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> models connect to and interact with external systems, enabling them to access real-time data and perform        <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> actions beyond their training data.                                                                             <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>                                                                                                                 <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> **Key Goals:**                                                                                                  <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> - Create a universal standard for AI-external system integration                                                <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> - Enable secure, controlled access to tools and data sources                                                    <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> - Streamline AI tool adoption across different models and environments                                          <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> - Provide a standardized alternative to proprietary solutions like OpenAI's function calling                    <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>                                                                                                                 <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> ## 2. Key Technical Features and Capabilities                                                                   <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>                                                                                                                 <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> **Core Technical Features:**                                                                                    <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> - **Standardized Communication Protocol**: Defines how LLMs communicate with external services                  <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> - **Security Framework**: Built-in security measures with user approval requirements for actions                <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> - **Tool Integration**: Seamless connection to various external tools and APIs                                  <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> - **Data Source Access**: Direct integration with databases, file systems, and web services                     <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> - **Cross-Platform Compatibility**: Works across multiple AI models and environments                            <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> - **Resource Management**: Utilities for debugging and managing external resources                              <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>                                                                                                                 <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> **Protocol Capabilities:**                                                                                      <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> - Real-time data access and retrieval                                                                           <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> - External tool execution with proper authorization                                                             <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> - Bidirectional communication between AI models and external systems                                            <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> - Standardized error handling and response formatting                                                           <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>                                                                                                                 <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> ## 3. Architectural Design and How It Works                                                                     <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>                                                                                                                 <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> **Architecture Components:**                                                                                    <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> - **MCP Servers**: Provide access to specific tools or data sources                                             <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> - **MCP Clients**: AI applications that consume MCP services                                                    <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> - **Base Protocol**: Core communication standard defining message formats and interaction patterns              <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> - **Transport Layer**: Handles the actual communication between clients and servers                             <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>                                                                                                                 <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> **How It Works:**                                                                                               <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> 1. **Server Registration**: MCP servers register their available tools and capabilities                         <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> 2. **Client Discovery**: AI applications discover available MCP servers and their capabilities                  <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> 3. **Secure Communication**: All interactions go through the standardized protocol with security checks         <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> 4. **Action Execution**: Tools are executed with proper user approval and authorization                         <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> 5. **Response Handling**: Results are returned in standardized formats for AI model consumption                 <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>                                                                                                                 <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> ## 4. Main Use Cases and Applications                                                                           <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>                                                                                                                 <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> **Primary Use Cases:**                                                                                          <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> - **Database Integration**: Direct access to SQL databases and data warehouses                                  <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> - **File System Operations**: Reading, writing, and managing files and directories                              <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> - **Web Services Integration**: Connecting to REST APIs and web services                                        <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> - **Development Tools**: Integration with IDEs, version control, and development environments                   <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> - **Productivity Applications**: Calendar management, email systems, document processing                        <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> - **Real-time Data Access**: Stock prices, weather data, news feeds, and other live information                 <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>                                                                                                                 <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> **Application Scenarios:**                                                                                      <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> - AI assistants that need to access current information                                                         <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> - Development environments where AI helps with coding tasks                                                     <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> - Business applications requiring real-time data analysis                                                       <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> - Content management systems with AI-powered features                                                           <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>                                                                                                                 <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> ## 5. Current Development Status and Leadership                                                                 <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>                                                                                                                 <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> **Development Status:**                                                                                         <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> - **Active Development**: The protocol is under ongoing active development                                      <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> - **Open Standard**: MCP is positioned as an open protocol, not proprietary                                     <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> - **Multiple Versions**: Latest specifications and draft versions are available                                 <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> - **Community-Driven**: Appears to have community involvement in development                                    <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>                                                                                                                 <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> **Key Players:**                                                                                                <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> - **Anthropic**: Appears to be a major contributor to MCP development (based on Claude integration references)  <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> - **Open Source Community**: The protocol is designed as an open standard with community participation          <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>                                                                                                                 <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> ## 6. Notable Implementations and Examples                                                                      <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>                                                                                                                 <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> **Reference Server Implementations:**                                                                           <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> - **Everything Server**: Comprehensive tool access                                                              <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> - **Fetch Server**: Web content retrieval capabilities                                                          <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> - **Filesystem Server**: File system operations and management                                                  <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> - **Database Servers**: Various database integration examples                                                   <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> - **API Integration Servers**: Examples for different web service integrations                                  <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>                                                                                                                 <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> **Implementation Categories:**                                                                                  <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> - **Current Reference Servers**: Active, maintained examples for common use cases                               <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> - **Archived Servers**: Historical implementations for reference                                                <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> - **Specialized Tools**: Servers designed for specific productivity and development tasks                       <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>                                                                                                                 <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> **Getting Started Resources:**                                                                                  <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> - Comprehensive documentation for server setup                                                                  <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> - Client implementation guides                                                                                  <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> - Example code and templates                                                                                    <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> - Debugging and development utilities                                                                           <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>                                                                                                                 <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> ## Key Advantages Over Alternatives                                                                             <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>                                                                                                                 <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> **Compared to Proprietary Solutions:**                                                                          <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> - **Open Standard**: Not tied to a specific AI provider                                                         <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> - **Cross-Platform**: Works with multiple AI models                                                             <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> - **Community-Driven**: Open development process                                                                <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> - **Standardized Security**: Built-in security framework                                                        <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> - **                                                                                                            <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</span>\n", "</pre>\n"], "text/plain": ["\u001b[33m╭─\u001b[0m\u001b[33m───────────────────────────────────────────────\u001b[0m\u001b[33m 🔧 Tool Output \u001b[0m\u001b[33m────────────────────────────────────────────────\u001b[0m\u001b[33m─╮\u001b[0m\n", "\u001b[33m│\u001b[0m Based on my research of official documentation and technical sources, here's a comprehensive overview of the    \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m Model Context Protocol (MCP):                                                                                   \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m                                                                                                                 \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m ## 1. What MCP Is and Its Core Purpose/Goals                                                                    \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m                                                                                                                 \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m The Model Context Protocol (MCP) is an **open protocol designed for seamless integration between large language \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m model (LLM) applications and external data sources and tools**. Its core purpose is to standardize how AI       \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m models connect to and interact with external systems, enabling them to access real-time data and perform        \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m actions beyond their training data.                                                                             \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m                                                                                                                 \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m **Key Goals:**                                                                                                  \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m - Create a universal standard for AI-external system integration                                                \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m - Enable secure, controlled access to tools and data sources                                                    \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m - Streamline AI tool adoption across different models and environments                                          \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m - Provide a standardized alternative to proprietary solutions like OpenAI's function calling                    \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m                                                                                                                 \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m ## 2. Key Technical Features and Capabilities                                                                   \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m                                                                                                                 \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m **Core Technical Features:**                                                                                    \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m - **Standardized Communication Protocol**: Defines how LLMs communicate with external services                  \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m - **Security Framework**: Built-in security measures with user approval requirements for actions                \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m - **Tool Integration**: Seamless connection to various external tools and APIs                                  \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m - **Data Source Access**: Direct integration with databases, file systems, and web services                     \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m - **Cross-Platform Compatibility**: Works across multiple AI models and environments                            \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m - **Resource Management**: Utilities for debugging and managing external resources                              \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m                                                                                                                 \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m **Protocol Capabilities:**                                                                                      \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m - Real-time data access and retrieval                                                                           \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m - External tool execution with proper authorization                                                             \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m - Bidirectional communication between AI models and external systems                                            \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m - Standardized error handling and response formatting                                                           \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m                                                                                                                 \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m ## 3. Architectural Design and How It Works                                                                     \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m                                                                                                                 \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m **Architecture Components:**                                                                                    \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m - **MCP Servers**: Provide access to specific tools or data sources                                             \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m - **MCP Clients**: AI applications that consume MCP services                                                    \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m - **Base Protocol**: Core communication standard defining message formats and interaction patterns              \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m - **Transport Layer**: Handles the actual communication between clients and servers                             \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m                                                                                                                 \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m **How It Works:**                                                                                               \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m 1. **Server Registration**: MCP servers register their available tools and capabilities                         \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m 2. **Client Discovery**: AI applications discover available MCP servers and their capabilities                  \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m 3. **Secure Communication**: All interactions go through the standardized protocol with security checks         \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m 4. **Action Execution**: Tools are executed with proper user approval and authorization                         \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m 5. **Response Handling**: Results are returned in standardized formats for AI model consumption                 \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m                                                                                                                 \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m ## 4. Main Use Cases and Applications                                                                           \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m                                                                                                                 \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m **Primary Use Cases:**                                                                                          \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m - **Database Integration**: Direct access to SQL databases and data warehouses                                  \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m - **File System Operations**: Reading, writing, and managing files and directories                              \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m - **Web Services Integration**: Connecting to REST APIs and web services                                        \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m - **Development Tools**: Integration with IDEs, version control, and development environments                   \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m - **Productivity Applications**: Calendar management, email systems, document processing                        \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m - **Real-time Data Access**: Stock prices, weather data, news feeds, and other live information                 \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m                                                                                                                 \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m **Application Scenarios:**                                                                                      \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m - AI assistants that need to access current information                                                         \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m - Development environments where AI helps with coding tasks                                                     \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m - Business applications requiring real-time data analysis                                                       \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m - Content management systems with AI-powered features                                                           \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m                                                                                                                 \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m ## 5. Current Development Status and Leadership                                                                 \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m                                                                                                                 \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m **Development Status:**                                                                                         \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m - **Active Development**: The protocol is under ongoing active development                                      \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m - **Open Standard**: MCP is positioned as an open protocol, not proprietary                                     \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m - **Multiple Versions**: Latest specifications and draft versions are available                                 \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m - **Community-Driven**: Appears to have community involvement in development                                    \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m                                                                                                                 \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m **Key Players:**                                                                                                \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m - **Anthropic**: Appears to be a major contributor to MCP development (based on Claude integration references)  \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m - **Open Source Community**: The protocol is designed as an open standard with community participation          \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m                                                                                                                 \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m ## 6. Notable Implementations and Examples                                                                      \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m                                                                                                                 \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m **Reference Server Implementations:**                                                                           \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m - **Everything Server**: Comprehensive tool access                                                              \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m - **Fetch Server**: Web content retrieval capabilities                                                          \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m - **Filesystem Server**: File system operations and management                                                  \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m - **Database Servers**: Various database integration examples                                                   \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m - **API Integration Servers**: Examples for different web service integrations                                  \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m                                                                                                                 \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m **Implementation Categories:**                                                                                  \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m - **Current Reference Servers**: Active, maintained examples for common use cases                               \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m - **Archived Servers**: Historical implementations for reference                                                \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m - **Specialized Tools**: Servers designed for specific productivity and development tasks                       \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m                                                                                                                 \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m **Getting Started Resources:**                                                                                  \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m - Comprehensive documentation for server setup                                                                  \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m - Client implementation guides                                                                                  \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m - Example code and templates                                                                                    \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m - Debugging and development utilities                                                                           \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m                                                                                                                 \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m ## Key Advantages Over Alternatives                                                                             \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m                                                                                                                 \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m **Compared to Proprietary Solutions:**                                                                          \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m - **Open Standard**: Not tied to a specific AI provider                                                         \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m - **Cross-Platform**: Works with multiple AI models                                                             \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m - **Community-Driven**: Open development process                                                                \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m - **Standardized Security**: Built-in security framework                                                        \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m - **                                                                                                            \u001b[33m│\u001b[0m\n", "\u001b[33m╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">╭───────────────────────────────────────────────────── 📝 AI ─────────────────────────────────────────────────────╮</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>                                                                                                                 <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> 🔧 Tool Call: read_todos                                                                                        <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>    Args: {}                                                                                                     <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>    ID: toolu_01PiXKxFUNYo3SVC9SvXwsKP                                                                           <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</span>\n", "</pre>\n"], "text/plain": ["\u001b[37m╭─\u001b[0m\u001b[37m────────────────────────────────────────────────────\u001b[0m\u001b[37m 📝 AI \u001b[0m\u001b[37m────────────────────────────────────────────────────\u001b[0m\u001b[37m─╮\u001b[0m\n", "\u001b[37m│\u001b[0m                                                                                                                 \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m 🔧 Tool Call: read_todos                                                                                        \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m    Args: {}                                                                                                     \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m    ID: toolu_01PiXKxFUNYo3SVC9SvXwsKP                                                                           \u001b[37m│\u001b[0m\n", "\u001b[37m╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #808000; text-decoration-color: #808000\">╭──────────────────────────────────────────────── 🔧 Tool Output ─────────────────────────────────────────────────╮</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> Current TODO List:                                                                                              <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> 1. ⏳ Research Model Context Protocol (MCP) - definition, purpose, features, architecture, use cases, and       <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> current status (pending)                                                                                        <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> 2. ⏳ Synthesize findings into comprehensive overview for the user (pending)                                    <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</span>\n", "</pre>\n"], "text/plain": ["\u001b[33m╭─\u001b[0m\u001b[33m───────────────────────────────────────────────\u001b[0m\u001b[33m 🔧 Tool Output \u001b[0m\u001b[33m────────────────────────────────────────────────\u001b[0m\u001b[33m─╮\u001b[0m\n", "\u001b[33m│\u001b[0m Current TODO List:                                                                                              \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m 1. ⏳ Research Model Context Protocol (MCP) - definition, purpose, features, architecture, use cases, and       \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m current status (pending)                                                                                        \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m 2. ⏳ Synthesize findings into comprehensive overview for the user (pending)                                    \u001b[33m│\u001b[0m\n", "\u001b[33m╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">╭───────────────────────────────────────────────────── 📝 AI ─────────────────────────────────────────────────────╮</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>                                                                                                                 <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> 🔧 Tool Call: think_tool                                                                                        <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>    Args: {                                                                                                      <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>   \"reflection\": \"The research task has been completed successfully. The sub-agent provided comprehensive        <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> information about Model Context Protocol (MCP) covering all the key areas I requested:\\n\\n1. ✅ Definition and  <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> core purpose - MCP is an open protocol for LLM-external system integration\\n2. ✅ Technical features -          <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> standardized communication, security framework, tool integration\\n3. ✅ Architecture - servers, clients, base   <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> protocol, transport layer\\n4. ✅ Use cases - database integration, file systems, web services, development      <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> tools\\n5. ✅ Development status - actively developed, open standard, community-driven\\n6. ✅ Notable            <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> implementations - various server examples and reference implementations\\n\\nThe information appears              <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> comprehensive and well-structured. I have sufficient detail to provide the user with a thorough overview of     <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> MCP. I should now mark the research task as completed and move to synthesizing this information into a clear,   <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> user-friendly overview.\"                                                                                        <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> }                                                                                                               <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>    ID: toolu_011zVATFuMWkVYyE77wT4ERk                                                                           <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</span>\n", "</pre>\n"], "text/plain": ["\u001b[37m╭─\u001b[0m\u001b[37m────────────────────────────────────────────────────\u001b[0m\u001b[37m 📝 AI \u001b[0m\u001b[37m────────────────────────────────────────────────────\u001b[0m\u001b[37m─╮\u001b[0m\n", "\u001b[37m│\u001b[0m                                                                                                                 \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m 🔧 Tool Call: think_tool                                                                                        \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m    Args: {                                                                                                      \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m   \"reflection\": \"The research task has been completed successfully. The sub-agent provided comprehensive        \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m information about Model Context Protocol (MCP) covering all the key areas I requested:\\n\\n1. ✅ Definition and  \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m core purpose - MCP is an open protocol for LLM-external system integration\\n2. ✅ Technical features -          \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m standardized communication, security framework, tool integration\\n3. ✅ Architecture - servers, clients, base   \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m protocol, transport layer\\n4. ✅ Use cases - database integration, file systems, web services, development      \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m tools\\n5. ✅ Development status - actively developed, open standard, community-driven\\n6. ✅ Notable            \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m implementations - various server examples and reference implementations\\n\\nThe information appears              \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m comprehensive and well-structured. I have sufficient detail to provide the user with a thorough overview of     \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m MCP. I should now mark the research task as completed and move to synthesizing this information into a clear,   \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m user-friendly overview.\"                                                                                        \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m }                                                                                                               \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m    ID: toolu_011zVATFuMWkVYyE77wT4ERk                                                                           \u001b[37m│\u001b[0m\n", "\u001b[37m╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #808000; text-decoration-color: #808000\">╭──────────────────────────────────────────────── 🔧 Tool Output ─────────────────────────────────────────────────╮</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> Reflection recorded: The research task has been completed successfully. The sub-agent provided comprehensive    <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> information about Model Context Protocol (MCP) covering all the key areas I requested:                          <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>                                                                                                                 <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> 1. ✅ Definition and core purpose - MCP is an open protocol for LLM-external system integration                 <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> 2. ✅ Technical features - standardized communication, security framework, tool integration                     <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> 3. ✅ Architecture - servers, clients, base protocol, transport layer                                           <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> 4. ✅ Use cases - database integration, file systems, web services, development tools                           <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> 5. ✅ Development status - actively developed, open standard, community-driven                                  <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> 6. ✅ Notable implementations - various server examples and reference implementations                           <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>                                                                                                                 <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> The information appears comprehensive and well-structured. I have sufficient detail to provide the user with a  <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> thorough overview of MCP. I should now mark the research task as completed and move to synthesizing this        <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> information into a clear, user-friendly overview.                                                               <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</span>\n", "</pre>\n"], "text/plain": ["\u001b[33m╭─\u001b[0m\u001b[33m───────────────────────────────────────────────\u001b[0m\u001b[33m 🔧 Tool Output \u001b[0m\u001b[33m────────────────────────────────────────────────\u001b[0m\u001b[33m─╮\u001b[0m\n", "\u001b[33m│\u001b[0m Reflection recorded: The research task has been completed successfully. The sub-agent provided comprehensive    \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m information about Model Context Protocol (MCP) covering all the key areas I requested:                          \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m                                                                                                                 \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m 1. ✅ Definition and core purpose - MCP is an open protocol for LLM-external system integration                 \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m 2. ✅ Technical features - standardized communication, security framework, tool integration                     \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m 3. ✅ Architecture - servers, clients, base protocol, transport layer                                           \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m 4. ✅ Use cases - database integration, file systems, web services, development tools                           \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m 5. ✅ Development status - actively developed, open standard, community-driven                                  \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m 6. ✅ Notable implementations - various server examples and reference implementations                           \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m                                                                                                                 \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m The information appears comprehensive and well-structured. I have sufficient detail to provide the user with a  \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m thorough overview of MCP. I should now mark the research task as completed and move to synthesizing this        \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m information into a clear, user-friendly overview.                                                               \u001b[33m│\u001b[0m\n", "\u001b[33m╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">╭───────────────────────────────────────────────────── 📝 AI ─────────────────────────────────────────────────────╮</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>                                                                                                                 <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> 🔧 Tool Call: write_todos                                                                                       <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>    Args: {                                                                                                      <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>   \"todos\": [                                                                                                    <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>     {                                                                                                           <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>       \"content\": \"Research Model Context Protocol (MCP) - definition, purpose, features, architecture, use      <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> cases, and current status\",                                                                                     <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>       \"status\": \"completed\"                                                                                     <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>     },                                                                                                          <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>     {                                                                                                           <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>       \"content\": \"Synthesize findings into comprehensive overview for the user\",                                <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>       \"status\": \"in_progress\"                                                                                   <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>     }                                                                                                           <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>   ]                                                                                                             <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> }                                                                                                               <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>    ID: toolu_01MpnNi9Gox4Jjcuvo1jELbo                                                                           <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</span>\n", "</pre>\n"], "text/plain": ["\u001b[37m╭─\u001b[0m\u001b[37m────────────────────────────────────────────────────\u001b[0m\u001b[37m 📝 AI \u001b[0m\u001b[37m────────────────────────────────────────────────────\u001b[0m\u001b[37m─╮\u001b[0m\n", "\u001b[37m│\u001b[0m                                                                                                                 \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m 🔧 Tool Call: write_todos                                                                                       \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m    Args: {                                                                                                      \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m   \"todos\": [                                                                                                    \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m     {                                                                                                           \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m       \"content\": \"Research Model Context Protocol (MCP) - definition, purpose, features, architecture, use      \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m cases, and current status\",                                                                                     \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m       \"status\": \"completed\"                                                                                     \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m     },                                                                                                          \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m     {                                                                                                           \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m       \"content\": \"Synthesize findings into comprehensive overview for the user\",                                \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m       \"status\": \"in_progress\"                                                                                   \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m     }                                                                                                           \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m   ]                                                                                                             \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m }                                                                                                               \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m    ID: toolu_01MpnNi9Gox4Jjcuvo1jELbo                                                                           \u001b[37m│\u001b[0m\n", "\u001b[37m╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #808000; text-decoration-color: #808000\">╭──────────────────────────────────────────────── 🔧 Tool Output ─────────────────────────────────────────────────╮</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> Updated todo list to [{'content': 'Research Model Context Protocol (MCP) - definition, purpose, features,       <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> architecture, use cases, and current status', 'status': 'completed'}, {'content': 'Synthesize findings into     <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> comprehensive overview for the user', 'status': 'in_progress'}]                                                 <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</span>\n", "</pre>\n"], "text/plain": ["\u001b[33m╭─\u001b[0m\u001b[33m───────────────────────────────────────────────\u001b[0m\u001b[33m 🔧 Tool Output \u001b[0m\u001b[33m────────────────────────────────────────────────\u001b[0m\u001b[33m─╮\u001b[0m\n", "\u001b[33m│\u001b[0m Updated todo list to [{'content': 'Research Model Context Protocol (MCP) - definition, purpose, features,       \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m architecture, use cases, and current status', 'status': 'completed'}, {'content': 'Synthesize findings into     \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m comprehensive overview for the user', 'status': 'in_progress'}]                                                 \u001b[33m│\u001b[0m\n", "\u001b[33m╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">╭───────────────────────────────────────────────────── 📝 AI ─────────────────────────────────────────────────────╮</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> # Model Context Protocol (MCP) Overview                                                                         <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>                                                                                                                 <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> ## What is MCP?                                                                                                 <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>                                                                                                                 <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> **Model Context Protocol (MCP)** is an open protocol designed to standardize how large language models (LLMs)   <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> connect to and interact with external data sources and tools. Think of it as a universal \"bridge\" that allows   <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> AI models to securely access real-time information and perform actions beyond what they learned during          <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> training.                                                                                                       <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>                                                                                                                 <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> ## Core Purpose &amp; Goals                                                                                         <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>                                                                                                                 <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> MCP addresses a fundamental limitation of AI models: they can only work with information from their training    <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> data, which becomes outdated quickly. MCP solves this by:                                                       <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>                                                                                                                 <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> - **Standardizing AI-external system integration** across different models and platforms                        <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> - **Enabling secure, controlled access** to live data and tools                                                 <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> - **Providing an open alternative** to proprietary solutions like OpenAI's function calling                     <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> - **Streamlining AI tool adoption** across different environments                                               <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>                                                                                                                 <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> ## How It Works                                                                                                 <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>                                                                                                                 <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> ### Architecture                                                                                                <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> MCP uses a client-server architecture:                                                                          <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>                                                                                                                 <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> - **MCP Servers**: Provide access to specific tools or data sources (databases, APIs, file systems)             <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> - **MCP Clients**: AI applications that consume these services                                                  <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> - **Base Protocol**: Defines standardized communication formats and interaction patterns                        <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> - **Transport Layer**: Handles secure communication between clients and servers                                 <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>                                                                                                                 <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> ### Process Flow                                                                                                <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> 1. **Discovery**: AI applications find available MCP servers and their capabilities                             <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> 2. **Registration**: Servers register their tools and what they can do                                          <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> 3. **Secure Communication**: All interactions use standardized protocols with built-in security                 <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> 4. **Execution**: Tools run with proper user approval and authorization                                         <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> 5. **Response**: Results return in formats AI models can easily understand                                      <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>                                                                                                                 <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> ## Key Features                                                                                                 <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>                                                                                                                 <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> - **Universal Standard**: Works across multiple AI models and platforms                                         <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> - **Built-in Security**: Requires user approval for actions and includes security frameworks                    <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> - **Real-time Access**: Connects to live data sources and current information                                   <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> - **Tool Integration**: Seamlessly connects to various external tools and APIs                                  <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> - **Cross-platform Compatibility**: Not tied to any specific AI provider                                        <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> - **Resource Management**: Includes debugging and management utilities                                          <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>                                                                                                                 <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> ## Common Use Cases                                                                                             <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>                                                                                                                 <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> ### Data Access                                                                                                 <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> - **Databases**: Direct SQL database queries and data warehouse access                                          <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> - **File Systems**: Reading, writing, and managing files and directories                                        <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> - **Web Services**: Integration with REST APIs and online services                                              <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> - **Real-time Data**: Stock prices, weather, news feeds, and live information                                   <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>                                                                                                                 <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> ### Productivity &amp; Development                                                                                  <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> - **Development Tools**: IDE integration, version control, code management                                      <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> - **Business Applications**: Calendar management, email systems, document processing                            <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> - **Content Management**: AI-powered features in content systems                                                <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> - **Data Analysis**: Real-time business intelligence and analytics                                              <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>                                                                                                                 <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> ## Current Status                                                                                               <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>                                                                                                                 <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> MCP is actively developed as an **open standard** with community involvement. Key aspects:                      <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>                                                                                                                 <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> - **Active Development**: Ongoing updates and improvements                                                      <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> - **Open Source**: Not proprietary, available for anyone to implement                                           <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> - **Community-Driven**: Open development process with community participation                                   <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> - **Multiple Implementations**: Various reference servers and examples available                                <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>                                                                                                                 <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> ### Notable Implementations                                                                                     <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> - **Everything Server**: Comprehensive tool access                                                              <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> - **Filesystem Server**: File operations and management                                                         <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> - **Database Servers**: Various database integration examples                                                   <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> - **Fetch Server**: Web content retrieval                                                                       <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> - **API Integration Servers**: Examples for different web services                                              <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>                                                                                                                 <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> ## Why MCP Matters                                                                                              <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>                                                                                                                 <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> ### For Developers                                                                                              <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> - **Standardization**: No need to create custom integrations for each AI model                                  <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> - **Security**: Built-in security framework reduces implementation risks                                        <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> - **Flexibility**: Open standard allows customization and extension                                             <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>                                                                                                                 <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> ### For Users                                                                                                   <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> - **Better AI Capabilities**: AI assistants can access current information and perform real actions             <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> - **Consistency**: Similar experience across different AI applications                                          <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> - **Security**: Controlled access with user approval requirements                                               <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>                                                                                                                 <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> ### For Organizations                                                                                           <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> - **Future-Proof**: Open standard reduces vendor lock-in                                                        <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> - **Scalable**: Can integrate multiple tools and data sources systematically                                    <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> - **Cost-Effective**: Reduces development time for AI integrations                                              <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>                                                                                                                 <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> ## Getting Started                                                                                              <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>                                                                                                                 <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> MCP provides comprehensive documentation including:                                                             <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> - Server setup guides                                                                                           <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> - Client implementation examples                                                                                <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> - Reference code and templates                                                                                  <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> - Debugging and development utilities                                                                           <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>                                                                                                                 <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> The protocol is designed to be accessible to developers while providing the robust features needed for          <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> enterprise applications.                                                                                        <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>                                                                                                                 <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> ---                                                                                                             <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>                                                                                                                 <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> MCP represents a significant step toward making AI models more practical and useful by giving them standardized <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> access to the real world's data and tools, all while maintaining security and user control.                     <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>                                                                                                                 <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> 🔧 Tool Call: write_todos                                                                                       <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>    Args: {}                                                                                                     <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>    ID: toolu_019Gb5E2zn7p1s3VtfrowM2Z                                                                           <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</span>\n", "</pre>\n"], "text/plain": ["\u001b[37m╭─\u001b[0m\u001b[37m────────────────────────────────────────────────────\u001b[0m\u001b[37m 📝 AI \u001b[0m\u001b[37m────────────────────────────────────────────────────\u001b[0m\u001b[37m─╮\u001b[0m\n", "\u001b[37m│\u001b[0m # Model Context Protocol (MCP) Overview                                                                         \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m                                                                                                                 \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m ## What is MCP?                                                                                                 \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m                                                                                                                 \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m **Model Context Protocol (MCP)** is an open protocol designed to standardize how large language models (LLMs)   \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m connect to and interact with external data sources and tools. Think of it as a universal \"bridge\" that allows   \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m AI models to securely access real-time information and perform actions beyond what they learned during          \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m training.                                                                                                       \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m                                                                                                                 \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m ## Core Purpose & Goals                                                                                         \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m                                                                                                                 \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m MCP addresses a fundamental limitation of AI models: they can only work with information from their training    \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m data, which becomes outdated quickly. MCP solves this by:                                                       \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m                                                                                                                 \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m - **Standardizing AI-external system integration** across different models and platforms                        \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m - **Enabling secure, controlled access** to live data and tools                                                 \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m - **Providing an open alternative** to proprietary solutions like OpenAI's function calling                     \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m - **Streamlining AI tool adoption** across different environments                                               \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m                                                                                                                 \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m ## How It Works                                                                                                 \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m                                                                                                                 \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m ### Architecture                                                                                                \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m MCP uses a client-server architecture:                                                                          \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m                                                                                                                 \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m - **MCP Servers**: Provide access to specific tools or data sources (databases, APIs, file systems)             \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m - **MCP Clients**: AI applications that consume these services                                                  \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m - **Base Protocol**: Defines standardized communication formats and interaction patterns                        \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m - **Transport Layer**: Handles secure communication between clients and servers                                 \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m                                                                                                                 \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m ### Process Flow                                                                                                \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m 1. **Discovery**: AI applications find available MCP servers and their capabilities                             \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m 2. **Registration**: Servers register their tools and what they can do                                          \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m 3. **Secure Communication**: All interactions use standardized protocols with built-in security                 \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m 4. **Execution**: Tools run with proper user approval and authorization                                         \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m 5. **Response**: Results return in formats AI models can easily understand                                      \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m                                                                                                                 \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m ## Key Features                                                                                                 \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m                                                                                                                 \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m - **Universal Standard**: Works across multiple AI models and platforms                                         \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m - **Built-in Security**: Requires user approval for actions and includes security frameworks                    \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m - **Real-time Access**: Connects to live data sources and current information                                   \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m - **Tool Integration**: Seamlessly connects to various external tools and APIs                                  \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m - **Cross-platform Compatibility**: Not tied to any specific AI provider                                        \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m - **Resource Management**: Includes debugging and management utilities                                          \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m                                                                                                                 \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m ## Common Use Cases                                                                                             \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m                                                                                                                 \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m ### Data Access                                                                                                 \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m - **Databases**: Direct SQL database queries and data warehouse access                                          \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m - **File Systems**: Reading, writing, and managing files and directories                                        \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m - **Web Services**: Integration with REST APIs and online services                                              \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m - **Real-time Data**: Stock prices, weather, news feeds, and live information                                   \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m                                                                                                                 \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m ### Productivity & Development                                                                                  \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m - **Development Tools**: IDE integration, version control, code management                                      \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m - **Business Applications**: Calendar management, email systems, document processing                            \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m - **Content Management**: AI-powered features in content systems                                                \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m - **Data Analysis**: Real-time business intelligence and analytics                                              \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m                                                                                                                 \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m ## Current Status                                                                                               \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m                                                                                                                 \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m MCP is actively developed as an **open standard** with community involvement. Key aspects:                      \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m                                                                                                                 \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m - **Active Development**: Ongoing updates and improvements                                                      \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m - **Open Source**: Not proprietary, available for anyone to implement                                           \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m - **Community-Driven**: Open development process with community participation                                   \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m - **Multiple Implementations**: Various reference servers and examples available                                \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m                                                                                                                 \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m ### Notable Implementations                                                                                     \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m - **Everything Server**: Comprehensive tool access                                                              \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m - **Filesystem Server**: File operations and management                                                         \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m - **Database Servers**: Various database integration examples                                                   \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m - **Fetch Server**: Web content retrieval                                                                       \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m - **API Integration Servers**: Examples for different web services                                              \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m                                                                                                                 \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m ## Why MCP Matters                                                                                              \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m                                                                                                                 \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m ### For Developers                                                                                              \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m - **Standardization**: No need to create custom integrations for each AI model                                  \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m - **Security**: Built-in security framework reduces implementation risks                                        \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m - **Flexibility**: Open standard allows customization and extension                                             \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m                                                                                                                 \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m ### For Users                                                                                                   \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m - **Better AI Capabilities**: AI assistants can access current information and perform real actions             \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m - **Consistency**: Similar experience across different AI applications                                          \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m - **Security**: Controlled access with user approval requirements                                               \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m                                                                                                                 \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m ### For Organizations                                                                                           \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m - **Future-Proof**: Open standard reduces vendor lock-in                                                        \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m - **Scalable**: Can integrate multiple tools and data sources systematically                                    \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m - **Cost-Effective**: Reduces development time for AI integrations                                              \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m                                                                                                                 \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m ## Getting Started                                                                                              \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m                                                                                                                 \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m MCP provides comprehensive documentation including:                                                             \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m - Server setup guides                                                                                           \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m - Client implementation examples                                                                                \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m - Reference code and templates                                                                                  \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m - Debugging and development utilities                                                                           \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m                                                                                                                 \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m The protocol is designed to be accessible to developers while providing the robust features needed for          \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m enterprise applications.                                                                                        \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m                                                                                                                 \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m ---                                                                                                             \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m                                                                                                                 \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m MCP represents a significant step toward making AI models more practical and useful by giving them standardized \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m access to the real world's data and tools, all while maintaining security and user control.                     \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m                                                                                                                 \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m 🔧 Tool Call: write_todos                                                                                       \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m    Args: {}                                                                                                     \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m    ID: toolu_019Gb5E2zn7p1s3VtfrowM2Z                                                                           \u001b[37m│\u001b[0m\n", "\u001b[37m╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #808000; text-decoration-color: #808000\">╭──────────────────────────────────────────────── 🔧 Tool Output ─────────────────────────────────────────────────╮</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> Error: 1 validation error for write_todos                                                                       <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> todos                                                                                                           <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>   Field required                                                                                                <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>     For further information visit https://errors.pydantic.dev/2.11/v/missing                                    <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>  Please fix your mistakes.                                                                                      <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</span>\n", "</pre>\n"], "text/plain": ["\u001b[33m╭─\u001b[0m\u001b[33m───────────────────────────────────────────────\u001b[0m\u001b[33m 🔧 Tool Output \u001b[0m\u001b[33m────────────────────────────────────────────────\u001b[0m\u001b[33m─╮\u001b[0m\n", "\u001b[33m│\u001b[0m Error: 1 validation error for write_todos                                                                       \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m todos                                                                                                           \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m   Field required                                                                                                \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m     For further information visit https://errors.pydantic.dev/2.11/v/missing                                    \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m  Please fix your mistakes.                                                                                      \u001b[33m│\u001b[0m\n", "\u001b[33m╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">╭───────────────────────────────────────────────────── 📝 AI ─────────────────────────────────────────────────────╮</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>                                                                                                                 <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> 🔧 Tool Call: write_todos                                                                                       <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>    Args: {                                                                                                      <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>   \"todos\": [                                                                                                    <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>     {                                                                                                           <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>       \"content\": \"Research Model Context Protocol (MCP) - definition, purpose, features, architecture, use      <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> cases, and current status\",                                                                                     <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>       \"status\": \"completed\"                                                                                     <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>     },                                                                                                          <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>     {                                                                                                           <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>       \"content\": \"Synthesize findings into comprehensive overview for the user\",                                <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>       \"status\": \"completed\"                                                                                     <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>     }                                                                                                           <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>   ]                                                                                                             <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> }                                                                                                               <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>    ID: toolu_01Mzn5xMNu2LQDdSzAxC7d7n                                                                           <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</span>\n", "</pre>\n"], "text/plain": ["\u001b[37m╭─\u001b[0m\u001b[37m────────────────────────────────────────────────────\u001b[0m\u001b[37m 📝 AI \u001b[0m\u001b[37m────────────────────────────────────────────────────\u001b[0m\u001b[37m─╮\u001b[0m\n", "\u001b[37m│\u001b[0m                                                                                                                 \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m 🔧 Tool Call: write_todos                                                                                       \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m    Args: {                                                                                                      \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m   \"todos\": [                                                                                                    \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m     {                                                                                                           \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m       \"content\": \"Research Model Context Protocol (MCP) - definition, purpose, features, architecture, use      \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m cases, and current status\",                                                                                     \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m       \"status\": \"completed\"                                                                                     \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m     },                                                                                                          \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m     {                                                                                                           \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m       \"content\": \"Synthesize findings into comprehensive overview for the user\",                                \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m       \"status\": \"completed\"                                                                                     \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m     }                                                                                                           \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m   ]                                                                                                             \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m }                                                                                                               \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m    ID: toolu_01Mzn5xMNu2LQDdSzAxC7d7n                                                                           \u001b[37m│\u001b[0m\n", "\u001b[37m╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #808000; text-decoration-color: #808000\">╭──────────────────────────────────────────────── 🔧 Tool Output ─────────────────────────────────────────────────╮</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> Updated todo list to [{'content': 'Research Model Context Protocol (MCP) - definition, purpose, features,       <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> architecture, use cases, and current status', 'status': 'completed'}, {'content': 'Synthesize findings into     <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> comprehensive overview for the user', 'status': 'completed'}]                                                   <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</span>\n", "</pre>\n"], "text/plain": ["\u001b[33m╭─\u001b[0m\u001b[33m───────────────────────────────────────────────\u001b[0m\u001b[33m 🔧 Tool Output \u001b[0m\u001b[33m────────────────────────────────────────────────\u001b[0m\u001b[33m─╮\u001b[0m\n", "\u001b[33m│\u001b[0m Updated todo list to [{'content': 'Research Model Context Protocol (MCP) - definition, purpose, features,       \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m architecture, use cases, and current status', 'status': 'completed'}, {'content': 'Synthesize findings into     \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m comprehensive overview for the user', 'status': 'completed'}]                                                   \u001b[33m│\u001b[0m\n", "\u001b[33m╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">╭───────────────────────────────────────────────────── 📝 AI ─────────────────────────────────────────────────────╮</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> I've provided you with a comprehensive overview of Model Context Protocol (MCP). The key takeaway is that MCP   <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> is an open protocol that acts as a standardized bridge between AI models and external systems, enabling them to <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> access real-time data and tools securely.                                                                       <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>                                                                                                                 <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> The protocol is particularly valuable because it:                                                               <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> - Solves the \"stale data\" problem that AI models face                                                           <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> - Provides a universal standard that works across different AI platforms                                        <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> - Includes built-in security and user control mechanisms                                                        <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> - Is actively developed as an open standard rather than a proprietary solution                                  <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>                                                                                                                 <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> Whether you're a developer looking to integrate AI capabilities, a user wanting more powerful AI assistants, or <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> an organization planning AI implementations, MCP offers a promising approach to making AI models more practical <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> and capable in real-world applications.                                                                         <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</span>\n", "</pre>\n"], "text/plain": ["\u001b[37m╭─\u001b[0m\u001b[37m────────────────────────────────────────────────────\u001b[0m\u001b[37m 📝 AI \u001b[0m\u001b[37m────────────────────────────────────────────────────\u001b[0m\u001b[37m─╮\u001b[0m\n", "\u001b[37m│\u001b[0m I've provided you with a comprehensive overview of Model Context Protocol (MCP). The key takeaway is that MCP   \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m is an open protocol that acts as a standardized bridge between AI models and external systems, enabling them to \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m access real-time data and tools securely.                                                                       \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m                                                                                                                 \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m The protocol is particularly valuable because it:                                                               \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m - Solves the \"stale data\" problem that AI models face                                                           \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m - Provides a universal standard that works across different AI platforms                                        \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m - Includes built-in security and user control mechanisms                                                        \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m - Is actively developed as an open standard rather than a proprietary solution                                  \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m                                                                                                                 \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m Whether you're a developer looking to integrate AI capabilities, a user wanting more powerful AI assistants, or \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m an organization planning AI implementations, MCP offers a promising approach to making AI models more practical \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m and capable in real-world applications.                                                                         \u001b[37m│\u001b[0m\n", "\u001b[37m╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from utils import format_messages\n", "\n", "result = agent.invoke(\n", "    {\n", "        \"messages\": [\n", "            {\n", "                \"role\": \"user\",\n", "                \"content\": \"Give me an overview of Model Context Protocol (MCP).\",\n", "            }\n", "        ],\n", "    }\n", ")\n", "\n", "format_messages(result[\"messages\"])"]}, {"cell_type": "markdown", "id": "507dd00b", "metadata": {}, "source": ["Trace: \n", "https://smith.langchain.com/public/3a389ec6-8e6e-4f9e-9a82-0d0a9569e6f8/r\n", "<!-- https://smith.langchain.com/public/1df7a10e-1465-499c-a3e0-86c1d5429324/r -->"]}, {"cell_type": "markdown", "id": "8fc0df5d", "metadata": {}, "source": ["## Using the Deep Agent Package\n", "\n", "Now you understand the underlying patterns! \n", "\n", "You can [use the `deepagents` package](\n", "https://github.com/hwchase17/deepagents) as a simple abstraction:\n", "\n", "* It include the file system tools\n", "* It includes the todo tool\n", "* It includes the task tool\n", "\n", "You only need to supply the sub-agent and any tools you want the sub-agent to use."]}, {"cell_type": "code", "execution_count": 8, "id": "62da8411", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<IPython.core.display.Image object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from deepagents import create_deep_agent\n", "\n", "agent = create_deep_agent(\n", "    sub_agent_tools,\n", "    INSTRUCTIONS,\n", "    subagents=[research_sub_agent],\n", "    model=model,\n", ")\n", "\n", "# Show the agent\n", "display(Image(agent.get_graph(xray=True).draw_mermaid_png()))"]}, {"cell_type": "code", "execution_count": 9, "id": "613634c2", "metadata": {"scrolled": true}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #000080; text-decoration-color: #000080\">╭─────────────────────────────────────────────────── 🧑 Human ────────────────────────────────────────────────────╮</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> Give me an very brief overview of Model Context Protocol (MCP).                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</span>\n", "</pre>\n"], "text/plain": ["\u001b[34m╭─\u001b[0m\u001b[34m──────────────────────────────────────────────────\u001b[0m\u001b[34m 🧑 Human \u001b[0m\u001b[34m───────────────────────────────────────────────────\u001b[0m\u001b[34m─╮\u001b[0m\n", "\u001b[34m│\u001b[0m Give me an very brief overview of Model Context Protocol (MCP).                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">╭───────────────────────────────────────────────────── 📝 AI ─────────────────────────────────────────────────────╮</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> I'll help you get a brief overview of Model Context Protocol (MCP). Let me research this for you.               <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>                                                                                                                 <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> 🔧 Tool Call: write_todos                                                                                       <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>    Args: {                                                                                                      <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>   \"todos\": [                                                                                                    <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>     {                                                                                                           <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>       \"content\": \"Research Model Context Protocol (MCP) to provide a brief overview\",                           <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>       \"status\": \"pending\"                                                                                       <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>     }                                                                                                           <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>   ]                                                                                                             <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> }                                                                                                               <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>    ID: toolu_01UuMFP92wVPFoJzzfWLM5LY                                                                           <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</span>\n", "</pre>\n"], "text/plain": ["\u001b[37m╭─\u001b[0m\u001b[37m────────────────────────────────────────────────────\u001b[0m\u001b[37m 📝 AI \u001b[0m\u001b[37m────────────────────────────────────────────────────\u001b[0m\u001b[37m─╮\u001b[0m\n", "\u001b[37m│\u001b[0m I'll help you get a brief overview of Model Context Protocol (MCP). Let me research this for you.               \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m                                                                                                                 \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m 🔧 Tool Call: write_todos                                                                                       \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m    Args: {                                                                                                      \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m   \"todos\": [                                                                                                    \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m     {                                                                                                           \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m       \"content\": \"Research Model Context Protocol (MCP) to provide a brief overview\",                           \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m       \"status\": \"pending\"                                                                                       \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m     }                                                                                                           \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m   ]                                                                                                             \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m }                                                                                                               \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m    ID: toolu_01UuMFP92wVPFoJzzfWLM5LY                                                                           \u001b[37m│\u001b[0m\n", "\u001b[37m╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #808000; text-decoration-color: #808000\">╭──────────────────────────────────────────────── 🔧 Tool Output ─────────────────────────────────────────────────╮</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> Updated todo list to [{'content': 'Research Model Context Protocol (MCP) to provide a brief overview',          <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> 'status': 'pending'}]                                                                                           <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</span>\n", "</pre>\n"], "text/plain": ["\u001b[33m╭─\u001b[0m\u001b[33m───────────────────────────────────────────────\u001b[0m\u001b[33m 🔧 Tool Output \u001b[0m\u001b[33m────────────────────────────────────────────────\u001b[0m\u001b[33m─╮\u001b[0m\n", "\u001b[33m│\u001b[0m Updated todo list to [{'content': 'Research Model Context Protocol (MCP) to provide a brief overview',          \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m 'status': 'pending'}]                                                                                           \u001b[33m│\u001b[0m\n", "\u001b[33m╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">╭───────────────────────────────────────────────────── 📝 AI ─────────────────────────────────────────────────────╮</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>                                                                                                                 <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> 🔧 Tool Call: ls                                                                                                <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>    Args: {}                                                                                                     <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>    ID: toolu_01J5pArn97sWwiqMMHheM7eT                                                                           <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</span>\n", "</pre>\n"], "text/plain": ["\u001b[37m╭─\u001b[0m\u001b[37m────────────────────────────────────────────────────\u001b[0m\u001b[37m 📝 AI \u001b[0m\u001b[37m────────────────────────────────────────────────────\u001b[0m\u001b[37m─╮\u001b[0m\n", "\u001b[37m│\u001b[0m                                                                                                                 \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m 🔧 Tool Call: ls                                                                                                \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m    Args: {}                                                                                                     \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m    ID: toolu_01J5pArn97sWwiqMMHheM7eT                                                                           \u001b[37m│\u001b[0m\n", "\u001b[37m╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #808000; text-decoration-color: #808000\">╭──────────────────────────────────────────────── 🔧 Tool Output ─────────────────────────────────────────────────╮</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>                                                                                                                 <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</span>\n", "</pre>\n"], "text/plain": ["\u001b[33m╭─\u001b[0m\u001b[33m───────────────────────────────────────────────\u001b[0m\u001b[33m 🔧 Tool Output \u001b[0m\u001b[33m────────────────────────────────────────────────\u001b[0m\u001b[33m─╮\u001b[0m\n", "\u001b[33m│\u001b[0m                                                                                                                 \u001b[33m│\u001b[0m\n", "\u001b[33m╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">╭───────────────────────────────────────────────────── 📝 AI ─────────────────────────────────────────────────────╮</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>                                                                                                                 <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> 🔧 Tool Call: write_file                                                                                        <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>    Args: {                                                                                                      <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>   \"file_path\": \"user_request.md\",                                                                               <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>   \"content\": \"# User Request\\n\\nGive me a very brief overview of Model Context Protocol (MCP).\\n\\n##            <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> Requirements\\n- Brief overview\\n- Focus on key concepts and purpose\"                                            <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> }                                                                                                               <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>    ID: toolu_01Wxm3tdXnRyjRS3YhztMArk                                                                           <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</span>\n", "</pre>\n"], "text/plain": ["\u001b[37m╭─\u001b[0m\u001b[37m────────────────────────────────────────────────────\u001b[0m\u001b[37m 📝 AI \u001b[0m\u001b[37m────────────────────────────────────────────────────\u001b[0m\u001b[37m─╮\u001b[0m\n", "\u001b[37m│\u001b[0m                                                                                                                 \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m 🔧 Tool Call: write_file                                                                                        \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m    Args: {                                                                                                      \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m   \"file_path\": \"user_request.md\",                                                                               \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m   \"content\": \"# User Request\\n\\nGive me a very brief overview of Model Context Protocol (MCP).\\n\\n##            \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m Requirements\\n- Brief overview\\n- Focus on key concepts and purpose\"                                            \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m }                                                                                                               \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m    ID: toolu_01Wxm3tdXnRyjRS3YhztMArk                                                                           \u001b[37m│\u001b[0m\n", "\u001b[37m╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #808000; text-decoration-color: #808000\">╭──────────────────────────────────────────────── 🔧 Tool Output ─────────────────────────────────────────────────╮</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> Updated file user_request.md                                                                                    <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</span>\n", "</pre>\n"], "text/plain": ["\u001b[33m╭─\u001b[0m\u001b[33m───────────────────────────────────────────────\u001b[0m\u001b[33m 🔧 Tool Output \u001b[0m\u001b[33m────────────────────────────────────────────────\u001b[0m\u001b[33m─╮\u001b[0m\n", "\u001b[33m│\u001b[0m Updated file user_request.md                                                                                    \u001b[33m│\u001b[0m\n", "\u001b[33m╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">╭───────────────────────────────────────────────────── 📝 AI ─────────────────────────────────────────────────────╮</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>                                                                                                                 <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> 🔧 Tool Call: write_todos                                                                                       <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>    Args: {                                                                                                      <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>   \"todos\": [                                                                                                    <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>     {                                                                                                           <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>       \"content\": \"Research Model Context Protocol (MCP) to provide a brief overview\",                           <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>       \"status\": \"in_progress\"                                                                                   <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>     }                                                                                                           <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>   ]                                                                                                             <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> }                                                                                                               <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>    ID: toolu_01LcKeM48k2nhujZTAjrCjtb                                                                           <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</span>\n", "</pre>\n"], "text/plain": ["\u001b[37m╭─\u001b[0m\u001b[37m────────────────────────────────────────────────────\u001b[0m\u001b[37m 📝 AI \u001b[0m\u001b[37m────────────────────────────────────────────────────\u001b[0m\u001b[37m─╮\u001b[0m\n", "\u001b[37m│\u001b[0m                                                                                                                 \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m 🔧 Tool Call: write_todos                                                                                       \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m    Args: {                                                                                                      \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m   \"todos\": [                                                                                                    \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m     {                                                                                                           \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m       \"content\": \"Research Model Context Protocol (MCP) to provide a brief overview\",                           \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m       \"status\": \"in_progress\"                                                                                   \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m     }                                                                                                           \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m   ]                                                                                                             \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m }                                                                                                               \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m    ID: toolu_01LcKeM48k2nhujZTAjrCjtb                                                                           \u001b[37m│\u001b[0m\n", "\u001b[37m╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #808000; text-decoration-color: #808000\">╭──────────────────────────────────────────────── 🔧 Tool Output ─────────────────────────────────────────────────╮</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> Updated todo list to [{'content': 'Research Model Context Protocol (MCP) to provide a brief overview',          <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> 'status': 'in_progress'}]                                                                                       <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</span>\n", "</pre>\n"], "text/plain": ["\u001b[33m╭─\u001b[0m\u001b[33m───────────────────────────────────────────────\u001b[0m\u001b[33m 🔧 Tool Output \u001b[0m\u001b[33m────────────────────────────────────────────────\u001b[0m\u001b[33m─╮\u001b[0m\n", "\u001b[33m│\u001b[0m Updated todo list to [{'content': 'Research Model Context Protocol (MCP) to provide a brief overview',          \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m 'status': 'in_progress'}]                                                                                       \u001b[33m│\u001b[0m\n", "\u001b[33m╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">╭───────────────────────────────────────────────────── 📝 AI ─────────────────────────────────────────────────────╮</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>                                                                                                                 <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> 🔧 Tool Call: task                                                                                              <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>    Args: {                                                                                                      <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>   \"description\": \"Research Model Context Protocol (MCP) and provide a comprehensive overview covering: what it  <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> is, its purpose, key features, how it works, who developed it, and its main use cases. Focus on providing       <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> clear, factual information about this protocol.\",                                                               <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>   \"subagent_type\": \"research-agent\"                                                                             <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> }                                                                                                               <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>    ID: toolu_01WVPBr44EDyeoSWW3TupubT                                                                           <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</span>\n", "</pre>\n"], "text/plain": ["\u001b[37m╭─\u001b[0m\u001b[37m────────────────────────────────────────────────────\u001b[0m\u001b[37m 📝 AI \u001b[0m\u001b[37m────────────────────────────────────────────────────\u001b[0m\u001b[37m─╮\u001b[0m\n", "\u001b[37m│\u001b[0m                                                                                                                 \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m 🔧 Tool Call: task                                                                                              \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m    Args: {                                                                                                      \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m   \"description\": \"Research Model Context Protocol (MCP) and provide a comprehensive overview covering: what it  \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m is, its purpose, key features, how it works, who developed it, and its main use cases. Focus on providing       \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m clear, factual information about this protocol.\",                                                               \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m   \"subagent_type\": \"research-agent\"                                                                             \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m }                                                                                                               \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m    ID: toolu_01WVPBr44EDyeoSWW3TupubT                                                                           \u001b[37m│\u001b[0m\n", "\u001b[37m╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #808000; text-decoration-color: #808000\">╭──────────────────────────────────────────────── 🔧 Tool Output ─────────────────────────────────────────────────╮</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> Based on my research, here's a comprehensive overview of the Model Context Protocol (MCP):                      <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>                                                                                                                 <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> ## What is Model Context Protocol (MCP)?                                                                        <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>                                                                                                                 <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> The Model Context Protocol (MCP) is a universal standard developed by Anthropic for integrating AI language     <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> models with external tools, data sources, and APIs. It serves as a standardized communication protocol that     <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> enables AI assistants to interact dynamically with various external systems and maintain context across         <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> different interactions.                                                                                         <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>                                                                                                                 <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> ## Developer                                                                                                    <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>                                                                                                                 <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> MCP was developed by **Anthropic**, the company behind the Claude AI assistant. It was introduced as part of    <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> <PERSON><PERSON><PERSON>'s efforts to improve AI system integration and functionality.                                         <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>                                                                                                                 <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> ## Purpose                                                                                                      <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>                                                                                                                 <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> The primary purposes of MCP include:                                                                            <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>                                                                                                                 <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> - **Standardizing AI Integration**: Providing a unified approach for connecting large language models (LLMs)    <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> with external tools and services                                                                                <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> - **Reducing Development Complexity**: Eliminating the need for custom integrations and APIs for each tool or   <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> service                                                                                                         <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> - **Enhancing AI Capabilities**: Enabling AI systems to access real-time data and perform actions beyond text   <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> generation                                                                                                      <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> - **Improving Context Continuity**: Allowing AI systems to maintain better memory and context across            <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> interactions                                                                                                    <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> - **Enabling Personalization**: Supporting more personalized AI experiences through consistent data access      <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>                                                                                                                 <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> ## Key Features                                                                                                 <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>                                                                                                                 <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> 1. **Client-Server Architecture**: MCP uses a bidirectional client-server model that enables real-time          <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> interactions between AI assistants and external systems                                                         <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>                                                                                                                 <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> 2. **Universal Standard**: Provides a consistent framework that works across different tools and platforms      <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> without requiring custom development                                                                            <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>                                                                                                                 <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> 3. **Dynamic Interactions**: Supports real-time, bidirectional communication rather than static API calls       <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>                                                                                                                 <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> 4. **Context Sharing**: Enables AI models to share and maintain context across different interactions and       <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> sessions                                                                                                        <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>                                                                                                                 <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> 5. **Tool Integration**: Provides capabilities for accessing tools, resources, and prompts within AI workflows  <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>                                                                                                                 <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> 6. **Data Access**: Allows AI systems to access and manipulate real-time data from various sources              <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>                                                                                                                 <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> ## How It Works                                                                                                 <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>                                                                                                                 <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> MCP operates through a **client-server architecture** where:                                                    <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>                                                                                                                 <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> - **MCP Servers**: Host and manage specific tools, data sources, or services (e.g., GitHub, Slack, databases)   <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> - **MCP Clients**: AI applications (like <PERSON>) that connect to and interact with these servers                <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> - **Bidirectional Communication**: Both clients and servers can initiate interactions, enabling proactive and   <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> reactive behaviors                                                                                              <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> - **Standardized Protocol**: Uses a consistent communication standard that eliminates the need for custom       <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> integrations                                                                                                    <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>                                                                                                                 <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> The protocol enables AI systems to become more context-aware and proactive by providing seamless access to      <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> external resources and maintaining continuity across interactions.                                              <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>                                                                                                                 <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> ## Main Use Cases                                                                                               <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>                                                                                                                 <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> 1. **Software Development**:                                                                                    <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>    - Integration with GitHub for code repository access                                                         <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>    - Development tool connectivity                                                                              <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>    - Code analysis and manipulation                                                                             <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>                                                                                                                 <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> 2. **Communication Platforms**:                                                                                 <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>    - Slack integration for team collaboration                                                                   <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>    - Other messaging platform connections                                                                       <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>                                                                                                                 <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> 3. **E-commerce Applications**:                                                                                 <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>    - Product catalog access                                                                                     <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>    - Inventory management                                                                                       <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>    - Customer service automation                                                                                <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>                                                                                                                 <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> 4. **Regulated Industries**:                                                                                    <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>    - Compliance monitoring                                                                                      <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>    - Data governance                                                                                            <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>    - Secure system integrations                                                                                 <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>                                                                                                                 <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> 5. **Enterprise Applications**:                                                                                 <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>    - Database connectivity                                                                                      <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>    - Business system integration                                                                                <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>    - Workflow automation                                                                                        <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>                                                                                                                 <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> 6. **Personal Productivity**:                                                                                   <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>    - Calendar management                                                                                        <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>    - Task automation                                                                                            <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>    - Document processing                                                                                        <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>                                                                                                                 <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> ## Benefits                                                                                                     <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>                                                                                                                 <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> - **Efficiency**: Significantly reduces integration complexity compared to traditional API approaches           <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> - **Consistency**: Maintains consistent development practices across different integrations                     <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> - **Scalability**: Enables easy addition of new tools and services without major architectural changes          <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> - **Real-time Capability**: Supports dynamic, real-time interactions with external systems                      <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> - **Reduced Development Time**: Minimizes the time needed to integrate new tools and services                   <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span>                                                                                                                 <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> MCP represents a significant advancement in AI system architecture, making it easier for AI assistants to       <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> interact with the broader digital ecosystem while maintaining security, consistency, and ease of use.           <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</span>\n", "</pre>\n"], "text/plain": ["\u001b[33m╭─\u001b[0m\u001b[33m───────────────────────────────────────────────\u001b[0m\u001b[33m 🔧 Tool Output \u001b[0m\u001b[33m────────────────────────────────────────────────\u001b[0m\u001b[33m─╮\u001b[0m\n", "\u001b[33m│\u001b[0m Based on my research, here's a comprehensive overview of the Model Context Protocol (MCP):                      \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m                                                                                                                 \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m ## What is Model Context Protocol (MCP)?                                                                        \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m                                                                                                                 \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m The Model Context Protocol (MCP) is a universal standard developed by Anthropic for integrating AI language     \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m models with external tools, data sources, and APIs. It serves as a standardized communication protocol that     \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m enables AI assistants to interact dynamically with various external systems and maintain context across         \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m different interactions.                                                                                         \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m                                                                                                                 \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m ## <PERSON><PERSON><PERSON>                                                                                                    \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m                                                                                                                 \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m MCP was developed by **Anthropic**, the company behind the Claude AI assistant. It was introduced as part of    \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m <PERSON><PERSON><PERSON>'s efforts to improve AI system integration and functionality.                                         \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m                                                                                                                 \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m ## Purpose                                                                                                      \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m                                                                                                                 \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m The primary purposes of MCP include:                                                                            \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m                                                                                                                 \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m - **Standardizing AI Integration**: Providing a unified approach for connecting large language models (LLMs)    \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m with external tools and services                                                                                \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m - **Reducing Development Complexity**: Eliminating the need for custom integrations and APIs for each tool or   \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m service                                                                                                         \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m - **Enhancing AI Capabilities**: Enabling AI systems to access real-time data and perform actions beyond text   \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m generation                                                                                                      \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m - **Improving Context Continuity**: Allowing AI systems to maintain better memory and context across            \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m interactions                                                                                                    \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m - **Enabling Personalization**: Supporting more personalized AI experiences through consistent data access      \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m                                                                                                                 \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m ## Key Features                                                                                                 \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m                                                                                                                 \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m 1. **Client-Server Architecture**: MCP uses a bidirectional client-server model that enables real-time          \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m interactions between AI assistants and external systems                                                         \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m                                                                                                                 \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m 2. **Universal Standard**: Provides a consistent framework that works across different tools and platforms      \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m without requiring custom development                                                                            \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m                                                                                                                 \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m 3. **Dynamic Interactions**: Supports real-time, bidirectional communication rather than static API calls       \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m                                                                                                                 \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m 4. **Context Sharing**: Enables AI models to share and maintain context across different interactions and       \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m sessions                                                                                                        \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m                                                                                                                 \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m 5. **Tool Integration**: Provides capabilities for accessing tools, resources, and prompts within AI workflows  \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m                                                                                                                 \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m 6. **Data Access**: Allows AI systems to access and manipulate real-time data from various sources              \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m                                                                                                                 \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m ## How It Works                                                                                                 \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m                                                                                                                 \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m MCP operates through a **client-server architecture** where:                                                    \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m                                                                                                                 \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m - **MCP Servers**: Host and manage specific tools, data sources, or services (e.g., GitHub, Slack, databases)   \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m - **MCP Clients**: AI applications (like <PERSON>) that connect to and interact with these servers                \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m - **Bidirectional Communication**: Both clients and servers can initiate interactions, enabling proactive and   \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m reactive behaviors                                                                                              \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m - **Standardized Protocol**: Uses a consistent communication standard that eliminates the need for custom       \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m integrations                                                                                                    \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m                                                                                                                 \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m The protocol enables AI systems to become more context-aware and proactive by providing seamless access to      \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m external resources and maintaining continuity across interactions.                                              \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m                                                                                                                 \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m ## Main Use Cases                                                                                               \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m                                                                                                                 \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m 1. **Software Development**:                                                                                    \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m    - Integration with GitHub for code repository access                                                         \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m    - Development tool connectivity                                                                              \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m    - Code analysis and manipulation                                                                             \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m                                                                                                                 \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m 2. **Communication Platforms**:                                                                                 \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m    - Slack integration for team collaboration                                                                   \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m    - Other messaging platform connections                                                                       \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m                                                                                                                 \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m 3. **E-commerce Applications**:                                                                                 \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m    - Product catalog access                                                                                     \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m    - Inventory management                                                                                       \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m    - Customer service automation                                                                                \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m                                                                                                                 \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m 4. **Regulated Industries**:                                                                                    \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m    - Compliance monitoring                                                                                      \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m    - Data governance                                                                                            \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m    - Secure system integrations                                                                                 \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m                                                                                                                 \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m 5. **Enterprise Applications**:                                                                                 \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m    - Database connectivity                                                                                      \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m    - Business system integration                                                                                \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m    - Workflow automation                                                                                        \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m                                                                                                                 \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m 6. **Personal Productivity**:                                                                                   \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m    - Calendar management                                                                                        \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m    - Task automation                                                                                            \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m    - Document processing                                                                                        \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m                                                                                                                 \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m ## Benefits                                                                                                     \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m                                                                                                                 \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m - **Efficiency**: Significantly reduces integration complexity compared to traditional API approaches           \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m - **Consistency**: Maintains consistent development practices across different integrations                     \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m - **Scalability**: Enables easy addition of new tools and services without major architectural changes          \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m - **Real-time Capability**: Supports dynamic, real-time interactions with external systems                      \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m - **Reduced Development Time**: Minimizes the time needed to integrate new tools and services                   \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m                                                                                                                 \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m MCP represents a significant advancement in AI system architecture, making it easier for AI assistants to       \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m interact with the broader digital ecosystem while maintaining security, consistency, and ease of use.           \u001b[33m│\u001b[0m\n", "\u001b[33m╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">╭───────────────────────────────────────────────────── 📝 AI ─────────────────────────────────────────────────────╮</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>                                                                                                                 <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> 🔧 Tool Call: write_todos                                                                                       <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>    Args: {                                                                                                      <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>   \"todos\": [                                                                                                    <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>     {                                                                                                           <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>       \"content\": \"Research Model Context Protocol (MCP) to provide a brief overview\",                           <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>       \"status\": \"completed\"                                                                                     <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>     }                                                                                                           <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>   ]                                                                                                             <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> }                                                                                                               <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>    ID: toolu_01CRvwjY5Zj3wN4XbS8hzFJk                                                                           <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</span>\n", "</pre>\n"], "text/plain": ["\u001b[37m╭─\u001b[0m\u001b[37m────────────────────────────────────────────────────\u001b[0m\u001b[37m 📝 AI \u001b[0m\u001b[37m────────────────────────────────────────────────────\u001b[0m\u001b[37m─╮\u001b[0m\n", "\u001b[37m│\u001b[0m                                                                                                                 \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m 🔧 Tool Call: write_todos                                                                                       \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m    Args: {                                                                                                      \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m   \"todos\": [                                                                                                    \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m     {                                                                                                           \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m       \"content\": \"Research Model Context Protocol (MCP) to provide a brief overview\",                           \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m       \"status\": \"completed\"                                                                                     \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m     }                                                                                                           \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m   ]                                                                                                             \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m }                                                                                                               \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m    ID: toolu_01CRvwjY5Zj3wN4XbS8hzFJk                                                                           \u001b[37m│\u001b[0m\n", "\u001b[37m╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #808000; text-decoration-color: #808000\">╭──────────────────────────────────────────────── 🔧 Tool Output ─────────────────────────────────────────────────╮</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> Updated todo list to [{'content': 'Research Model Context Protocol (MCP) to provide a brief overview',          <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> 'status': 'completed'}]                                                                                         <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</span>\n", "</pre>\n"], "text/plain": ["\u001b[33m╭─\u001b[0m\u001b[33m───────────────────────────────────────────────\u001b[0m\u001b[33m 🔧 Tool Output \u001b[0m\u001b[33m────────────────────────────────────────────────\u001b[0m\u001b[33m─╮\u001b[0m\n", "\u001b[33m│\u001b[0m Updated todo list to [{'content': 'Research Model Context Protocol (MCP) to provide a brief overview',          \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m 'status': 'completed'}]                                                                                         \u001b[33m│\u001b[0m\n", "\u001b[33m╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">╭───────────────────────────────────────────────────── 📝 AI ─────────────────────────────────────────────────────╮</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> ## Model Context Protocol (MCP) - Brief Overview                                                                <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>                                                                                                                 <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> **Model Context Protocol (MCP)** is a universal standard developed by **Anthropic** that enables AI language    <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> models to seamlessly integrate with external tools, data sources, and APIs.                                     <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>                                                                                                                 <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> **Key Points:**                                                                                                 <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>                                                                                                                 <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> - **Purpose**: Standardizes how AI assistants connect to external systems, eliminating the need for custom      <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> integrations                                                                                                    <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> - **Architecture**: Uses a client-server model where MCP servers host tools/data and MCP clients (like <PERSON>)  <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> connect to them                                                                                                 <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> - **Benefits**: Enables real-time data access, maintains context across interactions, and supports dynamic tool <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> usage                                                                                                           <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> - **Use Cases**: Software development (GitHub integration), communication platforms (Slack), e-commerce,        <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> enterprise applications, and personal productivity tools                                                        <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>                                                                                                                 <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> **Bottom Line**: MCP makes it much easier for AI systems to interact with the broader digital ecosystem by      <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> providing a standardized \"language\" for these connections, rather than requiring custom development for each    <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> integration.                                                                                                    <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</span>\n", "</pre>\n"], "text/plain": ["\u001b[37m╭─\u001b[0m\u001b[37m────────────────────────────────────────────────────\u001b[0m\u001b[37m 📝 AI \u001b[0m\u001b[37m────────────────────────────────────────────────────\u001b[0m\u001b[37m─╮\u001b[0m\n", "\u001b[37m│\u001b[0m ## Model Context Protocol (MCP) - Brief Overview                                                                \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m                                                                                                                 \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m **Model Context Protocol (MCP)** is a universal standard developed by **Anthropic** that enables AI language    \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m models to seamlessly integrate with external tools, data sources, and APIs.                                     \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m                                                                                                                 \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m **Key Points:**                                                                                                 \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m                                                                                                                 \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m - **Purpose**: Standardizes how AI assistants connect to external systems, eliminating the need for custom      \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m integrations                                                                                                    \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m - **Architecture**: Uses a client-server model where MCP servers host tools/data and MCP clients (like <PERSON>)  \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m connect to them                                                                                                 \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m - **Benefits**: Enables real-time data access, maintains context across interactions, and supports dynamic tool \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m usage                                                                                                           \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m - **Use Cases**: Software development (GitHub integration), communication platforms (Slack), e-commerce,        \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m enterprise applications, and personal productivity tools                                                        \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m                                                                                                                 \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m **Bottom Line**: MCP makes it much easier for AI systems to interact with the broader digital ecosystem by      \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m providing a standardized \"language\" for these connections, rather than requiring custom development for each    \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m integration.                                                                                                    \u001b[37m│\u001b[0m\n", "\u001b[37m╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["result = agent.invoke(\n", "    {\n", "        \"messages\": [\n", "            {\n", "                \"role\": \"user\",\n", "                \"content\": \"Give me an very brief overview of Model Context Protocol (MCP).\",\n", "            }\n", "        ],\n", "    }\n", ")\n", "\n", "format_messages(result[\"messages\"])"]}, {"cell_type": "markdown", "id": "fdcc6784", "metadata": {}, "source": ["Trace: \n", "https://smith.langchain.com/public/1d626d81-a102-4588-a2fb-cab40a7271f1/r\n", "<!-- https://smith.langchain.com/public/1ae2d7f6-f901-4ebd-b6c3-6657a55f88ae/r -->"]}, {"cell_type": "code", "execution_count": null, "id": "2d73925c", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}