{"cells": [{"cell_type": "code", "execution_count": 1, "id": "38cb48f0", "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "from dotenv import load_dotenv\n", "\n", "load_dotenv(os.path.join(\"..\", \".env\"), override=True)\n", "\n", "%load_ext autoreload\n", "%autoreload 2"]}, {"attachments": {"e94d5803-050b-4502-b784-f520a96c322b.png": {"image/png": "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"}}, "cell_type": "markdown", "id": "09dd8911", "metadata": {"vscode": {"languageId": "plaintext"}}, "source": ["## Planning: TODO Lists\n", "\n", "![Screenshot 2025-08-15 at 11.41.46 AM.png](attachment:e94d5803-050b-4502-b784-f520a96c322b.png)\n", "\n", "Many agents use TODO lists as a critical navigation tool for steering through long-running, complex tasks. Claude Code leverages [plan mode](https://www.anthropic.com/engineering/claude-code-best-practices) to create structured TODO lists before executing tasks, utilizing a specific tool called `TodoWrite` based on the [Claude Code prompt](https://cchistory.mariozechner.at/). Each TODO item contains two key components: content (a short, specific task description) and status (pending, in_progress, or completed).\n", "\n", "The challenge with TODO lists lies in maintaining attention as context windows grow—the average Manus task uses approximately 50 tool calls, creating substantial risk of [context rot](https://research.trychroma.com/context-rot). Agents become vulnerable to drifting off-topic or forgetting earlier objectives during lengthy conversations or complicated tasks. By continuously rewriting and updating the TODO list, agents like <PERSON><PERSON> effectively recite their objectives at the end of the context, helping to [stay focused on task](https://manus.im/blog/Context-Engineering-for-AI-Agents-Lessons-from-Building-Manus) and prevent mission drift.\n", "\n", "<!-- The style below reduces the gap between items in the same bulleted list. Run once per notebook -->\n", "<style>\n", "/* JupyterLab + classic notebook */\n", ".jp-RenderedHTMLCommon ul, .text_cell_render ul { margin-top: .25em; margin-bottom: .35em; padding-left: 1.2em; }\n", ".jp-RenderedHTMLCommon ul ul, .text_cell_render ul ul { margin-top: .15em; margin-bottom: .15em; padding-left: 1.0em; }\n", ".jp-RenderedHTMLCommon li, .text_cell_render li { margin: .1em 0; }\n", "</style>"]}, {"cell_type": "markdown", "id": "5971d1d7-a8e3-45cf-be54-5db30a3b0809", "metadata": {}, "source": ["### State\n", "\n", "Just as in the previous lesson, you will be using a `create_react_agent` with custom state.\n", "\n", "The state object serves as our primary mechanism for storing and passing context between different phases of the workflow. The State consists of the schema of the graph as well as reducer functions which specify how to apply updates to the state.\n", "\n", "There are three primary elements defined in the DeepAgent scheme: **`messages`**, **`todo`** and **`files`**.  \n", "\n", "- **`messages`** are inherited from `AgentState`, which was described in the first lesson.\n", "  - The `add_messages` reducer will append new messages to the end of the message list.  \n", "- **`todo`** are a list of `Todo` tasks. Each task has a description: `content` and a `status`: pending, in_progress, completed.\n", "  - No custom reducer is defined, so updates overwrite the list on write.\n", "- **`files`** is a virtual file system contained in state which you will explore in the next lesson.  \n"]}, {"cell_type": "code", "execution_count": 2, "id": "57472a38", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Overwriting ../src/deep_agents_from_scratch/state.py\n"]}], "source": ["%%writefile ../src/deep_agents_from_scratch/state.py\n", "\"\"\"State management for deep agents with TODO tracking and virtual file systems.\n", "\n", "This module defines the extended agent state structure that supports:\n", "- Task planning and progress tracking through TODO lists\n", "- Context offloading through a virtual file system stored in state\n", "- Efficient state merging with reducer functions\n", "\"\"\"\n", "\n", "from typing import Annotated, Literal, NotRequired\n", "from typing_extensions import TypedDict\n", "\n", "from langgraph.prebuilt.chat_agent_executor import AgentState\n", "\n", "\n", "class Todo(TypedDict):\n", "    \"\"\"A structured task item for tracking progress through complex workflows.\n", "\n", "    Attributes:\n", "        content: Short, specific description of the task\n", "        status: Current state - pending, in_progress, or completed\n", "    \"\"\"\n", "\n", "    content: str\n", "    status: Literal[\"pending\", \"in_progress\", \"completed\"]\n", "\n", "\n", "def file_reducer(left, right):\n", "    \"\"\"Merge two file dictionaries, with right side taking precedence.\n", "\n", "    Used as a reducer function for the files field in agent state,\n", "    allowing incremental updates to the virtual file system.\n", "\n", "    Args:\n", "        left: Left side dictionary (existing files)\n", "        right: Right side dictionary (new/updated files)\n", "\n", "    Returns:\n", "        Merged dictionary with right values overriding left values\n", "    \"\"\"\n", "    if left is None:\n", "        return right\n", "    elif right is None:\n", "        return left\n", "    else:\n", "        return {**left, **right}\n", "\n", "\n", "class DeepAgentState(AgentState):\n", "    \"\"\"Extended agent state that includes task tracking and virtual file system.\n", "\n", "    Inherits from LangGraph's AgentState and adds:\n", "    - todos: List of Todo items for task planning and progress tracking\n", "    - files: Virtual file system stored as dict mapping filenames to content\n", "    \"\"\"\n", "\n", "    todos: NotRequired[list[Todo]]\n", "    files: Annotated[NotRequired[dict[str, str]], file_reducer]"]}, {"cell_type": "markdown", "id": "93c1a330", "metadata": {}, "source": ["### Tool Description  - <PERSON><PERSON>\n", "As described above, long-running agents can use a todo list to stay on task. To enable that, todo tools, `write_todo` and `read_todo` are created. The tool description below is provided to the LLM detailing when to use the todo list, what it contains, and how to read and update it.   \n", "Note, that while the list contains individual tasks, it is updated with a full rewrite of the list. This allows the LLM to reconsider tasks as it makes progress. \n", "\n"]}, {"cell_type": "code", "execution_count": 3, "id": "797bbfc9", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #000080; text-decoration-color: #000080\">╭──────────────────────────────────────────────────── </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">Prompt</span><span style=\"color: #000080; text-decoration-color: #000080\"> ─────────────────────────────────────────────────────╮</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  Create and manage structured task lists for tracking progress through complex workflows.                       <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  <span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">## When to Use</span>                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  - Multi-step or non-trivial tasks requiring coordination                                                       <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  - When user provides multiple tasks or explicitly requests todo list                                           <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  - Avoid for single, trivial actions                                                                            <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  <span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">## Structure</span>                                                                                                   <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  - Maintain one list containing multiple todo objects (content, status, id)                                     <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  - Use clear, actionable content descriptions                                                                   <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  - Status must be: pending, in_progress, or completed                                                           <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  <span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">## Best Practices  </span>                                                                                            <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  - Only one in_progress task at a time                                                                          <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  - Mark completed immediately when task is fully done                                                           <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  - Always send the full updated list when making changes                                                        <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  - Prune irrelevant items to keep list focused                                                                  <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  <span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">## Progress Updates</span>                                                                                            <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  - Call TodoWrite again to change task status or edit content                                                   <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  - Reflect real-time progress; don't batch completions                                                          <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  - If blocked, keep in_progress and add new task describing blocker                                             <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  <span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">## Parameters</span>                                                                                                  <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  - todos: List of TODO items with content and status fields                                                     <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  <span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">## Returns</span>                                                                                                     <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  Updates agent state with new todo list.                                                                        <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</span>\n", "</pre>\n"], "text/plain": ["\u001b[34m╭─\u001b[0m\u001b[34m───────────────────────────────────────────────────\u001b[0m\u001b[34m \u001b[0m\u001b[1;32mPrompt\u001b[0m\u001b[34m \u001b[0m\u001b[34m────────────────────────────────────────────────────\u001b[0m\u001b[34m─╮\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  Create and manage structured task lists for tracking progress through complex workflows.                       \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  \u001b[1;35m## When to Use\u001b[0m                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  - Multi-step or non-trivial tasks requiring coordination                                                       \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  - When user provides multiple tasks or explicitly requests todo list                                           \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  - Avoid for single, trivial actions                                                                            \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  \u001b[1;35m## Structure\u001b[0m                                                                                                   \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  - Maintain one list containing multiple todo objects (content, status, id)                                     \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  - Use clear, actionable content descriptions                                                                   \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  - Status must be: pending, in_progress, or completed                                                           \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  \u001b[1;35m## Best Practices  \u001b[0m                                                                                            \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  - Only one in_progress task at a time                                                                          \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  - Mark completed immediately when task is fully done                                                           \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  - Always send the full updated list when making changes                                                        \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  - Prune irrelevant items to keep list focused                                                                  \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  \u001b[1;35m## Progress Updates\u001b[0m                                                                                            \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  - Call TodoWrite again to change task status or edit content                                                   \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  - Reflect real-time progress; don't batch completions                                                          \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  - If blocked, keep in_progress and add new task describing blocker                                             \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  \u001b[1;35m## Parameters\u001b[0m                                                                                                  \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  - todos: List of TODO items with content and status fields                                                     \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  \u001b[1;35m## Returns\u001b[0m                                                                                                     \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  Updates agent state with new todo list.                                                                        \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from utils import show_prompt\n", "\n", "from deep_agents_from_scratch.prompts import WRITE_TODOS_DESCRIPTION\n", "\n", "show_prompt(WRITE_TODOS_DESCRIPTION)"]}, {"cell_type": "markdown", "id": "0b87dea8", "metadata": {}, "source": ["### Write and Read ToDo Tools \n", "\n", "Below, you will create the **`write_todos`** and **`read_todos`** tools:  \n", "The **`write_todos`** tool takes a todo list from the LLM as an argument and writes it to state, overwriting any previous list. It then returns a `ToolMessage` containing the list that was written. Note that writing the list makes the information available to the LLM in the conversation history stored in `messages`, both in the LLM-generated tool call and in the returned `ToolMessage`.  \n", "\n", "The **`read_todos`** tool reads the todo list from state and returns it as a `ToolMessage`. It can be used to refresh the information in the LLM context.  \n", "\n", "\n", "Note that these tools will use some of the features that were discussed in the previous lesson:\n", "\n", "- `InjectedState` to provide the tool access to the graph state.\n", "- `Command` to update values in state.\n"]}, {"cell_type": "code", "execution_count": 4, "id": "2c7a2dd9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Overwriting ../src/deep_agents_from_scratch/todo_tools.py\n"]}], "source": ["%%writefile ../src/deep_agents_from_scratch/todo_tools.py\n", "\"\"\"TODO management tools for task planning and progress tracking.\n", "\n", "This module provides tools for creating and managing structured task lists\n", "that enable agents to plan complex workflows and track progress through\n", "multi-step operations.\n", "\"\"\"\n", "\n", "from typing import Annotated\n", "\n", "from langchain_core.messages import ToolMessage\n", "from langchain_core.tools import InjectedToolCallId, tool\n", "from langgraph.prebuilt import InjectedState\n", "from langgraph.types import Command\n", "\n", "from deep_agents_from_scratch.prompts import WRITE_TODOS_DESCRIPTION\n", "from deep_agents_from_scratch.state import DeepAgentState, Todo\n", "\n", "\n", "@tool(description=WRITE_TODOS_DESCRIPTION,parse_docstring=True)\n", "def write_todos(\n", "    todos: list[Todo], tool_call_id: Annotated[str, InjectedToolCallId]\n", ") -> Command:\n", "    \"\"\"Create or update the agent's TODO list for task planning and tracking.\n", "\n", "    Args:\n", "        todos: List of Todo items with content and status\n", "        tool_call_id: Tool call identifier for message response\n", "\n", "    Returns:\n", "        Command to update agent state with new TODO list\n", "    \"\"\"\n", "    return Command(\n", "        update={\n", "            \"todos\": todos,\n", "            \"messages\": [\n", "                ToolMessage(f\"Updated todo list to {todos}\", tool_call_id=tool_call_id)\n", "            ],\n", "        }\n", "    )\n", "\n", "\n", "@tool(parse_docstring=True)\n", "def read_todos(\n", "    state: Annotated[DeepAgentState, InjectedState],\n", "    tool_call_id: Annotated[str, InjectedToolCallId],\n", ") -> str:\n", "    \"\"\"Read the current TODO list from the agent state.\n", "\n", "    This tool allows the agent to retrieve and review the current TODO list\n", "    to stay focused on remaining tasks and track progress through complex workflows.\n", "\n", "    Args:\n", "        state: Injected agent state containing the current TODO list\n", "        tool_call_id: Injected tool call identifier for message tracking\n", "\n", "    Returns:\n", "        Formatted string representation of the current TODO list\n", "    \"\"\"\n", "    todos = state.get(\"todos\", [])\n", "    if not todos:\n", "        return \"No todos currently in the list.\"\n", "\n", "    result = \"Current TODO List:\\n\"\n", "    for i, todo in enumerate(todos, 1):\n", "        status_emoji = {\"pending\": \"⏳\", \"in_progress\": \"🔄\", \"completed\": \"✅\"}\n", "        emoji = status_emoji.get(todo[\"status\"], \"❓\")\n", "        result += f\"{i}. {emoji} {todo['content']} ({todo['status']})\\n\"\n", "\n", "    return result.strip()"]}, {"cell_type": "markdown", "id": "038dca59", "metadata": {}, "source": ["### Graph  \n", "\n", "As in the first lesson, you'll build an agent using `create_react_agent`.  We're going to focus on getting our agent to use a todo list. We'll follow the Manus approach of having a todo recitation after each task, and we'll use the tools you just created above."]}, {"cell_type": "code", "execution_count": 5, "id": "7c41f8fb", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #000080; text-decoration-color: #000080\">╭──────────────────────────────────────────────────── </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">Prompt</span><span style=\"color: #000080; text-decoration-color: #000080\"> ─────────────────────────────────────────────────────╮</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  Based upon the user's request:                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  1. Use the write_todos tool to create TODO at the start of a user request, per the tool description.           <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  2. After you accomplish a TODO, use the read_todos to read the TODOs in order to remind yourself of the plan.  <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  3. Reflect on what you've done and the TODO.                                                                   <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  4. Mark you task as completed, and proceed to the next TODO.                                                   <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  5. Continue this process until you have completed all TODOs.                                                   <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  IMPORTANT: Always create a research plan of TODOs and conduct research following the above guidelines for ANY  <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  user request.                                                                                                  <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  IMPORTANT: Aim to batch research tasks into a *single TODO* in order to minimize the number of TODOs you have  <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>  to keep track of.                                                                                              <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</span>\n", "</pre>\n"], "text/plain": ["\u001b[34m╭─\u001b[0m\u001b[34m───────────────────────────────────────────────────\u001b[0m\u001b[34m \u001b[0m\u001b[1;32mPrompt\u001b[0m\u001b[34m \u001b[0m\u001b[34m────────────────────────────────────────────────────\u001b[0m\u001b[34m─╮\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  Based upon the user's request:                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  1. Use the write_todos tool to create TODO at the start of a user request, per the tool description.           \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  2. After you accomplish a TODO, use the read_todos to read the TODOs in order to remind yourself of the plan.  \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  3. Reflect on what you've done and the TODO.                                                                   \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  4. Mark you task as completed, and proceed to the next TODO.                                                   \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  5. Continue this process until you have completed all TODOs.                                                   \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  IMPORTANT: Always create a research plan of TODOs and conduct research following the above guidelines for ANY  \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  user request.                                                                                                  \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  IMPORTANT: Aim to batch research tasks into a *single TODO* in order to minimize the number of TODOs you have  \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m  to keep track of.                                                                                              \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m│\u001b[0m                                                                                                                 \u001b[34m│\u001b[0m\n", "\u001b[34m╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from deep_agents_from_scratch.prompts import TODO_USAGE_INSTRUCTIONS\n", "\n", "show_prompt(TODO_USAGE_INSTRUCTIONS)"]}, {"cell_type": "code", "execution_count": 6, "id": "95c0fc53", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<IPython.core.display.Image object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from IPython.display import Image, display\n", "from langchain.chat_models import init_chat_model\n", "from langchain_core.tools import tool\n", "from langgraph.prebuilt import create_react_agent\n", "from utils import format_messages\n", "\n", "from deep_agents_from_scratch.prompts import TODO_USAGE_INSTRUCTIONS\n", "from deep_agents_from_scratch.state import DeepAgentState\n", "from deep_agents_from_scratch.todo_tools import read_todos, write_todos\n", "\n", "# Mock search result\n", "search_result = \"\"\"The Model Context Protocol (MCP) is an open standard protocol developed \n", "by Anthropic to enable seamless integration between AI models and external systems like \n", "tools, databases, and other services. It acts as a standardized communication layer, \n", "allowing AI models to access and utilize data from various sources in a consistent and \n", "efficient manner. Essentially, MCP simplifies the process of connecting AI assistants \n", "to external services by providing a unified language for data exchange. \"\"\"\n", "\n", "\n", "# Mock search tool\n", "@tool(parse_docstring=True)\n", "def web_search(\n", "    query: str,\n", "):\n", "    \"\"\"Search the web for information on a specific topic.\n", "\n", "    This tool performs web searches and returns relevant results\n", "    for the given query. Use this when you need to gather information from\n", "    the internet about any topic.\n", "\n", "    Args:\n", "        query: The search query string. Be specific and clear about what\n", "               information you're looking for.\n", "\n", "    Returns:\n", "        Search results from search engine.\n", "\n", "    Example:\n", "        web_search(\"machine learning applications in healthcare\")\n", "    \"\"\"\n", "    return search_result\n", "\n", "\n", "# Create agent using create_react_agent directly\n", "model = init_chat_model(model=\"anthropic:claude-sonnet-4-20250514\", temperature=0.0)\n", "tools = [write_todos, web_search, read_todos]\n", "\n", "# Add mock research instructions\n", "SIMPLE_RESEARCH_INSTRUCTIONS = \"\"\"IMPORTANT: Just make a single call to the web_search tool and use the result provided by the tool to answer the user's question.\"\"\"\n", "\n", "# Create agent\n", "agent = create_react_agent(\n", "    model,\n", "    tools,\n", "    prompt=TODO_USAGE_INSTRUCTIONS\n", "    + \"\\n\\n\"\n", "    + \"=\" * 80\n", "    + \"\\n\\n\"\n", "    + SIMPLE_RESEARCH_INSTRUCTIONS,\n", "    state_schema=DeepAgentState,\n", ")\n", "\n", "# Show the agent\n", "display(Image(agent.get_graph(xray=True).draw_mermaid_png()))"]}, {"cell_type": "markdown", "id": "0f2a042e", "metadata": {}, "source": ["Start the graph with no `todos` in state and an user research request. "]}, {"cell_type": "code", "execution_count": 7, "id": "3d2648f0", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #000080; text-decoration-color: #000080\">╭─────────────────────────────────────────────────── 🧑 Human ────────────────────────────────────────────────────╮</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">│</span> Give me a short summary of the Model Context Protocol (MCP).                                                    <span style=\"color: #000080; text-decoration-color: #000080\">│</span>\n", "<span style=\"color: #000080; text-decoration-color: #000080\">╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</span>\n", "</pre>\n"], "text/plain": ["\u001b[34m╭─\u001b[0m\u001b[34m──────────────────────────────────────────────────\u001b[0m\u001b[34m 🧑 Human \u001b[0m\u001b[34m───────────────────────────────────────────────────\u001b[0m\u001b[34m─╮\u001b[0m\n", "\u001b[34m│\u001b[0m Give me a short summary of the Model Context Protocol (MCP).                                                    \u001b[34m│\u001b[0m\n", "\u001b[34m╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">╭───────────────────────────────────────────────────── 📝 AI ─────────────────────────────────────────────────────╮</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> I'll help you get a summary of the Model Context Protocol (MCP). Let me search for current information about    <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> it.                                                                                                             <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>                                                                                                                 <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> 🔧 Tool Call: write_todos                                                                                       <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>    Args: {                                                                                                      <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>   \"todos\": [                                                                                                    <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>     {                                                                                                           <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>       \"content\": \"Search for information about Model Context Protocol (MCP) and provide a short summary\",       <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>       \"status\": \"pending\"                                                                                       <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>     }                                                                                                           <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>   ]                                                                                                             <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> }                                                                                                               <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>    ID: toolu_01Qi4kjW8RN63CzJK8S84SHp                                                                           <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</span>\n", "</pre>\n"], "text/plain": ["\u001b[37m╭─\u001b[0m\u001b[37m────────────────────────────────────────────────────\u001b[0m\u001b[37m 📝 AI \u001b[0m\u001b[37m────────────────────────────────────────────────────\u001b[0m\u001b[37m─╮\u001b[0m\n", "\u001b[37m│\u001b[0m I'll help you get a summary of the Model Context Protocol (MCP). Let me search for current information about    \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m it.                                                                                                             \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m                                                                                                                 \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m 🔧 Tool Call: write_todos                                                                                       \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m    Args: {                                                                                                      \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m   \"todos\": [                                                                                                    \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m     {                                                                                                           \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m       \"content\": \"Search for information about Model Context Protocol (MCP) and provide a short summary\",       \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m       \"status\": \"pending\"                                                                                       \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m     }                                                                                                           \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m   ]                                                                                                             \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m }                                                                                                               \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m    ID: toolu_01Qi4kjW8RN63CzJK8S84SHp                                                                           \u001b[37m│\u001b[0m\n", "\u001b[37m╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #808000; text-decoration-color: #808000\">╭──────────────────────────────────────────────── 🔧 Tool Output ─────────────────────────────────────────────────╮</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> Updated todo list to [{'content': 'Search for information about Model Context Protocol (MCP) and provide a      <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> short summary', 'status': 'pending'}]                                                                           <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</span>\n", "</pre>\n"], "text/plain": ["\u001b[33m╭─\u001b[0m\u001b[33m───────────────────────────────────────────────\u001b[0m\u001b[33m 🔧 Tool Output \u001b[0m\u001b[33m────────────────────────────────────────────────\u001b[0m\u001b[33m─╮\u001b[0m\n", "\u001b[33m│\u001b[0m Updated todo list to [{'content': 'Search for information about Model Context Protocol (MCP) and provide a      \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m short summary', 'status': 'pending'}]                                                                           \u001b[33m│\u001b[0m\n", "\u001b[33m╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">╭───────────────────────────────────────────────────── 📝 AI ─────────────────────────────────────────────────────╮</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>                                                                                                                 <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> 🔧 Tool Call: web_search                                                                                        <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>    Args: {                                                                                                      <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>   \"query\": \"Model Context Protocol MCP overview summary\"                                                        <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> }                                                                                                               <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>    ID: toolu_01JfzGZgXQqFPe3x7NKcfG19                                                                           <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</span>\n", "</pre>\n"], "text/plain": ["\u001b[37m╭─\u001b[0m\u001b[37m────────────────────────────────────────────────────\u001b[0m\u001b[37m 📝 AI \u001b[0m\u001b[37m────────────────────────────────────────────────────\u001b[0m\u001b[37m─╮\u001b[0m\n", "\u001b[37m│\u001b[0m                                                                                                                 \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m 🔧 Tool Call: web_search                                                                                        \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m    Args: {                                                                                                      \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m   \"query\": \"Model Context Protocol MCP overview summary\"                                                        \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m }                                                                                                               \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m    ID: toolu_01JfzGZgXQqFPe3x7NKcfG19                                                                           \u001b[37m│\u001b[0m\n", "\u001b[37m╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #808000; text-decoration-color: #808000\">╭──────────────────────────────────────────────── 🔧 Tool Output ─────────────────────────────────────────────────╮</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> The Model Context Protocol (MCP) is an open standard protocol developed                                         <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> by Anthropic to enable seamless integration between AI models and external systems like                         <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> tools, databases, and other services. It acts as a standardized communication layer,                            <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> allowing AI models to access and utilize data from various sources in a consistent and                          <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> efficient manner. Essentially, MCP simplifies the process of connecting AI assistants                           <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> to external services by providing a unified language for data exchange.                                         <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</span>\n", "</pre>\n"], "text/plain": ["\u001b[33m╭─\u001b[0m\u001b[33m───────────────────────────────────────────────\u001b[0m\u001b[33m 🔧 Tool Output \u001b[0m\u001b[33m────────────────────────────────────────────────\u001b[0m\u001b[33m─╮\u001b[0m\n", "\u001b[33m│\u001b[0m The Model Context Protocol (MCP) is an open standard protocol developed                                         \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m by <PERSON><PERSON><PERSON> to enable seamless integration between AI models and external systems like                         \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m tools, databases, and other services. It acts as a standardized communication layer,                            \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m allowing AI models to access and utilize data from various sources in a consistent and                          \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m efficient manner. Essentially, MCP simplifies the process of connecting AI assistants                           \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m to external services by providing a unified language for data exchange.                                         \u001b[33m│\u001b[0m\n", "\u001b[33m╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">╭───────────────────────────────────────────────────── 📝 AI ─────────────────────────────────────────────────────╮</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>                                                                                                                 <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> 🔧 Tool Call: read_todos                                                                                        <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>    Args: {}                                                                                                     <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>    ID: toolu_01BJqDSPcU3fwmAprw1zdofT                                                                           <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</span>\n", "</pre>\n"], "text/plain": ["\u001b[37m╭─\u001b[0m\u001b[37m────────────────────────────────────────────────────\u001b[0m\u001b[37m 📝 AI \u001b[0m\u001b[37m────────────────────────────────────────────────────\u001b[0m\u001b[37m─╮\u001b[0m\n", "\u001b[37m│\u001b[0m                                                                                                                 \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m 🔧 Tool Call: read_todos                                                                                        \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m    Args: {}                                                                                                     \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m    ID: toolu_01BJqDSPcU3fwmAprw1zdofT                                                                           \u001b[37m│\u001b[0m\n", "\u001b[37m╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #808000; text-decoration-color: #808000\">╭──────────────────────────────────────────────── 🔧 Tool Output ─────────────────────────────────────────────────╮</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> Current TODO List:                                                                                              <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> 1. ⏳ Search for information about Model Context Protocol (MCP) and provide a short summary (pending)           <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</span>\n", "</pre>\n"], "text/plain": ["\u001b[33m╭─\u001b[0m\u001b[33m───────────────────────────────────────────────\u001b[0m\u001b[33m 🔧 Tool Output \u001b[0m\u001b[33m────────────────────────────────────────────────\u001b[0m\u001b[33m─╮\u001b[0m\n", "\u001b[33m│\u001b[0m Current TODO List:                                                                                              \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m 1. ⏳ Search for information about Model Context Protocol (MCP) and provide a short summary (pending)           \u001b[33m│\u001b[0m\n", "\u001b[33m╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">╭───────────────────────────────────────────────────── 📝 AI ─────────────────────────────────────────────────────╮</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>                                                                                                                 <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> 🔧 Tool Call: write_todos                                                                                       <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>    Args: {                                                                                                      <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>   \"todos\": [                                                                                                    <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>     {                                                                                                           <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>       \"content\": \"Search for information about Model Context Protocol (MCP) and provide a short summary\",       <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>       \"status\": \"completed\"                                                                                     <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>     }                                                                                                           <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>   ]                                                                                                             <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> }                                                                                                               <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>    ID: toolu_01BVMvA15NPCaRs9EZmBJdZJ                                                                           <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</span>\n", "</pre>\n"], "text/plain": ["\u001b[37m╭─\u001b[0m\u001b[37m────────────────────────────────────────────────────\u001b[0m\u001b[37m 📝 AI \u001b[0m\u001b[37m────────────────────────────────────────────────────\u001b[0m\u001b[37m─╮\u001b[0m\n", "\u001b[37m│\u001b[0m                                                                                                                 \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m 🔧 Tool Call: write_todos                                                                                       \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m    Args: {                                                                                                      \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m   \"todos\": [                                                                                                    \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m     {                                                                                                           \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m       \"content\": \"Search for information about Model Context Protocol (MCP) and provide a short summary\",       \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m       \"status\": \"completed\"                                                                                     \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m     }                                                                                                           \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m   ]                                                                                                             \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m }                                                                                                               \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m    ID: toolu_01BVMvA15NPCaRs9EZmBJdZJ                                                                           \u001b[37m│\u001b[0m\n", "\u001b[37m╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #808000; text-decoration-color: #808000\">╭──────────────────────────────────────────────── 🔧 Tool Output ─────────────────────────────────────────────────╮</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> Updated todo list to [{'content': 'Search for information about Model Context Protocol (MCP) and provide a      <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">│</span> short summary', 'status': 'completed'}]                                                                         <span style=\"color: #808000; text-decoration-color: #808000\">│</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000\">╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</span>\n", "</pre>\n"], "text/plain": ["\u001b[33m╭─\u001b[0m\u001b[33m───────────────────────────────────────────────\u001b[0m\u001b[33m 🔧 Tool Output \u001b[0m\u001b[33m────────────────────────────────────────────────\u001b[0m\u001b[33m─╮\u001b[0m\n", "\u001b[33m│\u001b[0m Updated todo list to [{'content': 'Search for information about Model Context Protocol (MCP) and provide a      \u001b[33m│\u001b[0m\n", "\u001b[33m│\u001b[0m short summary', 'status': 'completed'}]                                                                         \u001b[33m│\u001b[0m\n", "\u001b[33m╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">╭───────────────────────────────────────────────────── 📝 AI ─────────────────────────────────────────────────────╮</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> ## Model Context Protocol (MCP) Summary                                                                         <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>                                                                                                                 <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> The **Model Context Protocol (MCP)** is an open standard protocol developed by Anthropic that serves as a       <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> standardized communication layer between AI models and external systems. Here are the key points:               <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>                                                                                                                 <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> **Purpose**: MCP enables seamless integration between AI assistants and various external services including:    <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> - Tools and applications                                                                                        <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> - Databases                                                                                                     <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> - APIs and other services                                                                                       <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>                                                                                                                 <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> **Key Benefits**:                                                                                               <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> - **Standardization**: Provides a unified language for data exchange                                            <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> - **Simplification**: Streamlines the process of connecting AI models to external resources                     <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> - **Efficiency**: Enables consistent and efficient data access across different systems                         <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>                                                                                                                 <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> **Function**: MCP acts as a bridge that allows AI models to access and utilize data from various sources in a   <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> standardized way, eliminating the need for custom integrations for each external service.                       <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>                                                                                                                 <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> In essence, MCP makes it easier for AI assistants to interact with the broader digital ecosystem by providing a <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span> common protocol for communication and data exchange.                                                            <span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">│</span>\n", "<span style=\"color: #c0c0c0; text-decoration-color: #c0c0c0\">╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</span>\n", "</pre>\n"], "text/plain": ["\u001b[37m╭─\u001b[0m\u001b[37m────────────────────────────────────────────────────\u001b[0m\u001b[37m 📝 AI \u001b[0m\u001b[37m────────────────────────────────────────────────────\u001b[0m\u001b[37m─╮\u001b[0m\n", "\u001b[37m│\u001b[0m ## Model Context Protocol (MCP) Summary                                                                         \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m                                                                                                                 \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m The **Model Context Protocol (MCP)** is an open standard protocol developed by Anthropic that serves as a       \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m standardized communication layer between AI models and external systems. Here are the key points:               \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m                                                                                                                 \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m **Purpose**: MCP enables seamless integration between AI assistants and various external services including:    \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m - Tools and applications                                                                                        \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m - Databases                                                                                                     \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m - APIs and other services                                                                                       \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m                                                                                                                 \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m **Key Benefits**:                                                                                               \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m - **Standardization**: Provides a unified language for data exchange                                            \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m - **Simplification**: Streamlines the process of connecting AI models to external resources                     \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m - **Efficiency**: Enables consistent and efficient data access across different systems                         \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m                                                                                                                 \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m **Function**: MCP acts as a bridge that allows AI models to access and utilize data from various sources in a   \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m standardized way, eliminating the need for custom integrations for each external service.                       \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m                                                                                                                 \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m In essence, MCP makes it easier for AI assistants to interact with the broader digital ecosystem by providing a \u001b[37m│\u001b[0m\n", "\u001b[37m│\u001b[0m common protocol for communication and data exchange.                                                            \u001b[37m│\u001b[0m\n", "\u001b[37m╰─────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Example usage\n", "result = agent.invoke(\n", "    {\n", "        \"messages\": [\n", "            {\n", "                \"role\": \"user\",\n", "                \"content\": \"Give me a short summary of the Model Context Protocol (MCP).\",\n", "            }\n", "        ],\n", "        \"todos\": [],\n", "    }\n", ")\n", "\n", "format_messages(result[\"messages\"])"]}, {"cell_type": "markdown", "id": "a1ee40fd", "metadata": {}, "source": ["Trace: \n", "https://smith.langchain.com/public/7e771e79-8996-4833-8242-2b3559c2c1d7/r\n", "<!-- https://smith.langchain.com/public/57e530e5-c3bb-4ed6-aff6-c57d06158fe9/r  -->"]}, {"cell_type": "markdown", "id": "b8b01e32", "metadata": {}, "source": []}, {"cell_type": "code", "execution_count": null, "id": "50b93063-f081-4ae6-803c-26d12d36aeed", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.13"}}, "nbformat": 4, "nbformat_minor": 5}