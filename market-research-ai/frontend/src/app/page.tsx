'use client'

import { motion } from 'framer-motion'
import EnhancedChatInterface from '@/components/chat/EnhancedChatInterface'
import Header from '@/components/shared/Header'

export default function Home() {
  const pageVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.6,
        staggerChildren: 0.1
      }
    }
  }

  return (
    <motion.div
      className="h-screen text-white font-sans flex flex-col overflow-hidden"
      variants={pageVariants}
      initial="hidden"
      animate="visible"
    >
      {/* Animated background */}
      <div className="fixed inset-0 bg-gradient-to-br from-background-start via-background-end to-background-start">
        <motion.div
          className="absolute inset-0 opacity-30"
          animate={{
            background: [
              "radial-gradient(circle at 20% 50%, rgba(99, 102, 241, 0.3) 0%, transparent 50%)",
              "radial-gradient(circle at 80% 50%, rgba(139, 92, 246, 0.3) 0%, transparent 50%)",
              "radial-gradient(circle at 50% 20%, rgba(99, 102, 241, 0.3) 0%, transparent 50%)",
            ]
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
      </div>

      {/* Header */}
      <Header />

      {/* Main Chat Interface */}
      <main className="flex-1 overflow-hidden relative z-10">
        <EnhancedChatInterface />
      </main>
    </motion.div>
  )
}
