'use client'

import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'

interface PanelProps {
  title?: string
  subtitle?: string
  children: React.ReactNode
  className?: string
  collapsible?: boolean
  collapsed?: boolean
  onToggle?: () => void
  actions?: React.ReactNode
  loading?: boolean
  error?: string
}

const Panel: React.FC<PanelProps> = ({
  title,
  subtitle,
  children,
  className = '',
  collapsible = false,
  collapsed = false,
  onToggle,
  actions,
  loading = false,
  error
}) => {
  const [isHovered, setIsHovered] = useState(false)

  const containerVariants = {
    hidden: { opacity: 0, scale: 0.95 },
    visible: { 
      opacity: 1, 
      scale: 1,
      transition: { duration: 0.3, ease: "easeOut" }
    }
  }

  const contentVariants = {
    hidden: { opacity: 0, height: 0 },
    visible: { 
      opacity: 1, 
      height: 'auto',
      transition: { duration: 0.3, ease: "easeOut" }
    }
  }

  return (
    <motion.div
      className={`glass-panel h-full flex flex-col relative overflow-hidden ${className}`}
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      onHoverStart={() => setIsHovered(true)}
      onHoverEnd={() => setIsHovered(false)}
    >
      {/* Header */}
      {(title || subtitle || collapsible || actions) && (
        <div className="flex items-center justify-between p-6 border-b border-white/10 bg-white/5 backdrop-blur-sm">
          <div className="flex-1">
            {title && (
              <div className="flex items-center gap-3">
                <h3 className="text-lg font-semibold text-white">{title}</h3>
                {loading && (
                  <div className="w-4 h-4 border-2 border-primary-500 border-t-transparent rounded-full animate-spin" />
                )}
              </div>
            )}
            {subtitle && (
              <p className="text-sm text-slate-400 mt-1">{subtitle}</p>
            )}
            {error && (
              <div className="flex items-center gap-2 mt-2 text-red-400 text-sm">
                <span className="material-symbols-outlined text-base">error</span>
                <span>{error}</span>
              </div>
            )}
          </div>
          
          <div className="flex items-center gap-2">
            {actions}
            
            {collapsible && (
              <button
                onClick={onToggle}
                className="btn-secondary p-2 rounded-lg hover:bg-white/10 transition-colors"
                title={collapsed ? 'Expand panel' : 'Collapse panel'}
              >
                <motion.span 
                  className="material-symbols-outlined"
                  animate={{ rotate: collapsed ? 180 : 0 }}
                  transition={{ duration: 0.2 }}
                >
                  expand_less
                </motion.span>
              </button>
            )}
          </div>
        </div>
      )}

      {/* Content */}
      <AnimatePresence>
        {!collapsed && (
          <motion.div
            className="flex-1 overflow-hidden"
            variants={contentVariants}
            initial="hidden"
            animate="visible"
            exit="hidden"
          >
            {loading ? (
              <div className="flex items-center justify-center h-full">
                <div className="text-center">
                  <div className="w-8 h-8 border-2 border-primary-500 border-t-transparent rounded-full animate-spin mx-auto mb-4" />
                  <p className="text-slate-400">Loading...</p>
                </div>
              </div>
            ) : error ? (
              <div className="flex items-center justify-center h-full">
                <div className="text-center max-w-md">
                  <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-red-500/10 flex items-center justify-center">
                    <span className="material-symbols-outlined text-2xl text-red-400">error</span>
                  </div>
                  <h4 className="text-lg font-semibold text-white mb-2">Something went wrong</h4>
                  <p className="text-slate-400 text-sm mb-4">{error}</p>
                  <button className="btn-primary text-sm px-4 py-2">
                    Try Again
                  </button>
                </div>
              </div>
            ) : (
              children
            )}
          </motion.div>
        )}
      </AnimatePresence>

      {/* Hover overlay for interactive feedback */}
      <AnimatePresence>
        {isHovered && !loading && !error && (
          <motion.div
            className="absolute inset-0 bg-gradient-to-br from-primary-500/5 to-transparent pointer-events-none"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.2 }}
          />
        )}
      </AnimatePresence>

      {/* Corner accent */}
      <div className="absolute top-0 right-0 w-16 h-16 bg-gradient-to-bl from-primary-500/10 to-transparent pointer-events-none" />
    </motion.div>
  )
}

export default Panel
