'use client'

import { useState, useRef, useEffect } from 'react'
import { motion } from 'framer-motion'
import { useMarketResearchStore } from '@/store'

interface SidebarProps {
  side: 'left' | 'right'
  title: string
  subtitle?: string
  children: React.ReactNode
  onResize?: (size: number) => void
  minWidth?: number
  maxWidth?: number
  defaultWidth?: number
}

const Sidebar: React.FC<SidebarProps> = ({
  side,
  title,
  subtitle,
  children,
  onResize,
  minWidth = 300,
  maxWidth = 600,
  defaultWidth = 400
}) => {
  const [isResizing, setIsResizing] = useState(false)
  const [width, setWidth] = useState(defaultWidth)
  const sidebarRef = useRef<HTMLDivElement>(null)
  const resizeHandleRef = useRef<HTMLDivElement>(null)

  const { toggleLeftSidebar, toggleRightSidebar } = useMarketResearchStore()

  const handleToggle = () => {
    if (side === 'left') {
      toggleLeftSidebar()
    } else {
      toggleRightSidebar()
    }
  }

  // Handle resize functionality
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (!isResizing || !sidebarRef.current) return

      const rect = sidebarRef.current.getBoundingClientRect()
      let newWidth: number

      if (side === 'left') {
        newWidth = e.clientX - rect.left
      } else {
        newWidth = rect.right - e.clientX
      }

      // Clamp width between min and max
      newWidth = Math.max(minWidth, Math.min(maxWidth, newWidth))
      
      setWidth(newWidth)
      onResize?.(newWidth)
    }

    const handleMouseUp = () => {
      setIsResizing(false)
      document.body.style.cursor = 'default'
      document.body.style.userSelect = 'auto'
    }

    if (isResizing) {
      document.addEventListener('mousemove', handleMouseMove)
      document.addEventListener('mouseup', handleMouseUp)
      document.body.style.cursor = side === 'left' ? 'ew-resize' : 'ew-resize'
      document.body.style.userSelect = 'none'
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove)
      document.removeEventListener('mouseup', handleMouseUp)
    }
  }, [isResizing, side, minWidth, maxWidth, onResize])

  const handleResizeStart = () => {
    setIsResizing(true)
  }

  return (
    <motion.div
      ref={sidebarRef}
      className="h-full glass-panel flex flex-col relative"
      style={{ width: `${width}px` }}
      initial={{ opacity: 0, x: side === 'left' ? -20 : 20 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ duration: 0.3 }}
    >
      {/* Header */}
      <div className="flex items-center justify-between p-6 border-b border-white/10">
        <div className="flex-1">
          <h2 className="text-xl font-bold text-white">{title}</h2>
          {subtitle && (
            <p className="text-sm text-slate-400 mt-1">{subtitle}</p>
          )}
        </div>
        
        <button
          onClick={handleToggle}
          className="btn-secondary p-2 rounded-xl hover:bg-white/10 transition-colors"
          title={`Collapse ${side} sidebar`}
        >
          <span className="material-symbols-outlined">
            {side === 'left' ? 'chevron_left' : 'chevron_right'}
          </span>
        </button>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-hidden">
        {children}
      </div>

      {/* Resize handle */}
      <div
        ref={resizeHandleRef}
        className={`absolute top-0 bottom-0 w-1 cursor-ew-resize hover:bg-primary-500/50 transition-colors ${
          side === 'left' ? 'right-0' : 'left-0'
        } ${isResizing ? 'bg-primary-500' : 'bg-transparent'}`}
        onMouseDown={handleResizeStart}
        title="Drag to resize"
      >
        {/* Visual indicator */}
        <div className={`absolute top-1/2 transform -translate-y-1/2 w-1 h-8 bg-white/20 rounded-full ${
          side === 'left' ? 'right-0' : 'left-0'
        }`} />
      </div>

      {/* Resize overlay during dragging */}
      {isResizing && (
        <div className="fixed inset-0 z-50 pointer-events-none">
          <div className="absolute inset-0 bg-black/20 backdrop-blur-sm" />
          <div className={`absolute top-0 bottom-0 w-px bg-primary-500 shadow-glow ${
            side === 'left' ? 'left-' + width + 'px' : 'right-' + width + 'px'
          }`} />
        </div>
      )}
    </motion.div>
  )
}

export default Sidebar
