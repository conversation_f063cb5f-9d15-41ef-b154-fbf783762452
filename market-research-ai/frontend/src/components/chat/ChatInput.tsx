'use client'

import { useState, useRef, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'

interface ChatInputProps {
  onSendMessage: (message: string) => void
  disabled?: boolean
  placeholder?: string
}

const ChatInput: React.FC<ChatInputProps> = ({
  onSendMessage,
  disabled = false,
  placeholder = "Type your message..."
}) => {
  const [message, setMessage] = useState('')
  const [isExpanded, setIsExpanded] = useState(false)
  const textareaRef = useRef<HTMLTextAreaElement>(null)

  // Auto-resize textarea
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto'
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`
    }
  }, [message])

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (message.trim() && !disabled) {
      onSendMessage(message.trim())
      setMessage('')
      setIsExpanded(false)
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSubmit(e)
    }
  }

  const quickSuggestions = [
    {
      text: "Analyze Bangkok cafe market trends",
      icon: "trending_up"
    },
    {
      text: "Find competitor analysis for restaurants",
      icon: "analytics"
    },
    {
      text: "Generate location recommendations",
      icon: "location_on"
    },
    {
      text: "Create market research report",
      icon: "description"
    }
  ]

  return (
    <div className="border-t border-white/10 bg-white/5 backdrop-blur-sm">
      {/* Quick Suggestions */}
      <AnimatePresence>
        {!isExpanded && message.length === 0 && (
          <motion.div
            className="p-4 border-b border-white/5"
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3 }}
          >
            <p className="text-xs text-slate-400 mb-3">Quick suggestions:</p>
            <div className="flex flex-wrap gap-2">
              {quickSuggestions.map((suggestion, index) => (
                <motion.button
                  key={index}
                  className="flex items-center gap-2 px-3 py-2 bg-white/5 hover:bg-white/10 border border-white/10 rounded-lg text-sm text-slate-300 hover:text-white transition-all duration-200"
                  onClick={() => {
                    setMessage(suggestion.text)
                    setIsExpanded(true)
                    textareaRef.current?.focus()
                  }}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <span className="material-symbols-outlined text-sm">{suggestion.icon}</span>
                  <span>{suggestion.text}</span>
                </motion.button>
              ))}
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Input Area */}
      <form onSubmit={handleSubmit} className="p-6">
        <div className="relative">
          <div className="flex items-end gap-4">
            {/* Textarea */}
            <div className="flex-1 relative">
              <textarea
                ref={textareaRef}
                value={message}
                onChange={(e) => {
                  setMessage(e.target.value)
                  setIsExpanded(e.target.value.length > 0)
                }}
                onKeyDown={handleKeyDown}
                onFocus={() => setIsExpanded(true)}
                onBlur={() => {
                  if (message.length === 0) {
                    setIsExpanded(false)
                  }
                }}
                placeholder={placeholder}
                disabled={disabled}
                className="w-full min-h-[50px] max-h-[200px] px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-slate-400 resize-none focus:outline-none focus:border-primary-500 focus:bg-white/15 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                rows={1}
              />

              {/* Character count */}
              <AnimatePresence>
                {isExpanded && message.length > 0 && (
                  <motion.div
                    className="absolute bottom-2 right-2 text-xs text-slate-500"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                  >
                    {message.length}/2000
                  </motion.div>
                )}
              </AnimatePresence>
            </div>

            {/* Action Buttons */}
            <div className="flex items-center gap-2">
              {/* Attachment Button */}
              <motion.button
                type="button"
                className="p-3 rounded-xl bg-white/5 hover:bg-white/10 border border-white/10 text-slate-400 hover:text-white transition-colors disabled:opacity-50"
                disabled={disabled}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                title="Attach file"
              >
                <span className="material-symbols-outlined">attach_file</span>
              </motion.button>

              {/* Voice Input Button */}
              <motion.button
                type="button"
                className="p-3 rounded-xl bg-white/5 hover:bg-white/10 border border-white/10 text-slate-400 hover:text-white transition-colors disabled:opacity-50"
                disabled={disabled}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                title="Voice input"
              >
                <span className="material-symbols-outlined">mic</span>
              </motion.button>

              {/* Send Button */}
              <motion.button
                type="submit"
                disabled={!message.trim() || disabled}
                className={`p-3 rounded-xl font-medium transition-all duration-200 ${
                  message.trim() && !disabled
                    ? 'bg-gradient-primary text-white shadow-glow hover:shadow-glow-purple'
                    : 'bg-white/5 text-slate-500 cursor-not-allowed'
                }`}
                whileHover={message.trim() && !disabled ? { scale: 1.05, y: -1 } : {}}
                whileTap={message.trim() && !disabled ? { scale: 0.95 } : {}}
              >
                {disabled ? (
                  <div className="w-5 h-5 border-2 border-slate-500 border-t-transparent rounded-full animate-spin" />
                ) : (
                  <span className="material-symbols-outlined">send</span>
                )}
              </motion.button>
            </div>
          </div>

          {/* Enhanced Features Bar */}
          <AnimatePresence>
            {isExpanded && (
              <motion.div
                className="flex items-center justify-between mt-3 pt-3 border-t border-white/10"
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                transition={{ duration: 0.2 }}
              >
                <div className="flex items-center gap-3">
                  <button
                    type="button"
                    className="text-xs text-slate-400 hover:text-white transition-colors flex items-center gap-1"
                    title="Format text"
                  >
                    <span className="material-symbols-outlined text-sm">format_bold</span>
                    Format
                  </button>
                  
                  <button
                    type="button"
                    className="text-xs text-slate-400 hover:text-white transition-colors flex items-center gap-1"
                    title="Add emoji"
                  >
                    <span className="material-symbols-outlined text-sm">sentiment_satisfied</span>
                    Emoji
                  </button>
                </div>

                <div className="text-xs text-slate-500">
                  Press <kbd className="px-1 py-0.5 bg-white/10 rounded text-xs">Enter</kbd> to send, 
                  <kbd className="px-1 py-0.5 bg-white/10 rounded text-xs ml-1">Shift+Enter</kbd> for new line
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </form>
    </div>
  )
}

export default ChatInput
