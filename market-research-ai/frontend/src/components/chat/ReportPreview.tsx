'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { useMarketResearchStore, useActiveConversation } from '@/store'

interface ReportPreviewProps {
  conversationId: string
  onClose: () => void
}

interface ReportSection {
  id: string
  title: string
  content: string
  type: 'summary' | 'analysis' | 'recommendations' | 'data'
  status: 'generating' | 'complete' | 'pending'
  confidence: number
}

const ReportPreview: React.FC<ReportPreviewProps> = ({ conversationId, onClose }) => {
  const conversation = useActiveConversation()
  const [reportSections, setReportSections] = useState<ReportSection[]>([])
  const [isGenerating, setIsGenerating] = useState(false)
  
  const { createReport, exportReport } = useMarketResearchStore()

  // Generate report sections based on conversation
  useEffect(() => {
    if (conversation && conversation.messages.length > 1) {
      generateReportSections()
    }
  }, [conversation?.messages])

  const generateReportSections = () => {
    if (!conversation) return

    setIsGenerating(true)

    // Mock report generation based on conversation content
    const sections: ReportSection[] = [
      {
        id: '1',
        title: 'Executive Summary',
        content: 'Based on our analysis of Bangkok\'s cafe and restaurant market, we\'ve identified key trends and opportunities in the F&B sector.',
        type: 'summary',
        status: 'complete',
        confidence: 0.95
      },
      {
        id: '2',
        title: 'Market Analysis',
        content: 'The Bangkok F&B market shows strong growth potential with increasing consumer demand for premium dining experiences and specialty coffee.',
        type: 'analysis',
        status: 'complete',
        confidence: 0.88
      },
      {
        id: '3',
        title: 'Competitive Landscape',
        content: 'Key competitors include established chains and emerging boutique establishments, particularly in high-traffic areas like Sukhumvit and Silom.',
        type: 'analysis',
        status: 'generating',
        confidence: 0.72
      },
      {
        id: '4',
        title: 'Recommendations',
        content: '',
        type: 'recommendations',
        status: 'pending',
        confidence: 0
      }
    ]

    setReportSections(sections)

    // Simulate progressive generation
    setTimeout(() => {
      setReportSections(prev => prev.map(section => 
        section.id === '3' 
          ? { ...section, status: 'complete', confidence: 0.91 }
          : section
      ))
    }, 2000)

    setTimeout(() => {
      setReportSections(prev => prev.map(section => 
        section.id === '4' 
          ? { 
              ...section, 
              content: 'We recommend focusing on premium locations in Thonglor and Ekkamai districts, targeting affluent young professionals and expatriates.',
              status: 'complete',
              confidence: 0.87
            }
          : section
      ))
      setIsGenerating(false)
    }, 4000)
  }

  const handleExportReport = () => {
    const reportTitle = `Market Research Report - ${conversation?.title || 'Untitled'}`
    createReport(reportTitle)
    exportReport('new-report-id', 'pdf')
  }

  const getSectionIcon = (type: string) => {
    switch (type) {
      case 'summary': return 'summarize'
      case 'analysis': return 'analytics'
      case 'recommendations': return 'lightbulb'
      case 'data': return 'table_chart'
      default: return 'description'
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'complete': return 'text-green-400'
      case 'generating': return 'text-yellow-400'
      case 'pending': return 'text-slate-500'
      default: return 'text-slate-400'
    }
  }

  return (
    <div className="h-full glass-panel flex flex-col">
      {/* Header */}
      <div className="flex items-center justify-between p-6 border-b border-white/10">
        <div>
          <h3 className="text-lg font-semibold text-white">Live Report</h3>
          <p className="text-sm text-slate-400">Auto-generated from conversation</p>
        </div>
        
        <div className="flex items-center gap-2">
          <button
            onClick={handleExportReport}
            className="btn-primary text-sm px-3 py-2 rounded-lg"
            disabled={isGenerating}
          >
            <span className="material-symbols-outlined mr-1 text-sm">download</span>
            Export
          </button>
          
          <button
            onClick={onClose}
            className="btn-secondary p-2 rounded-lg"
          >
            <span className="material-symbols-outlined">close</span>
          </button>
        </div>
      </div>

      {/* Report Content */}
      <div className="flex-1 overflow-y-auto p-6">
        {reportSections.length === 0 ? (
          <div className="text-center py-12">
            <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-white/5 flex items-center justify-center">
              <span className="material-symbols-outlined text-2xl text-slate-400">description</span>
            </div>
            <h4 className="text-lg font-medium text-white mb-2">No Report Yet</h4>
            <p className="text-slate-400 text-sm">
              Start a conversation to automatically generate a market research report
            </p>
          </div>
        ) : (
          <div className="space-y-6">
            {/* Report Title */}
            <div className="text-center pb-6 border-b border-white/10">
              <h2 className="text-xl font-bold text-white mb-2">
                Market Research Report
              </h2>
              <p className="text-slate-400 text-sm">
                Generated from: {conversation?.title}
              </p>
              <p className="text-slate-500 text-xs mt-1">
                {new Date().toLocaleDateString()} • {reportSections.filter(s => s.status === 'complete').length}/{reportSections.length} sections complete
              </p>
            </div>

            {/* Report Sections */}
            <AnimatePresence>
              {reportSections.map((section, index) => (
                <motion.div
                  key={section.id}
                  className="border border-white/10 rounded-xl p-4 bg-white/5"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                >
                  {/* Section Header */}
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center gap-3">
                      <div className={`${getStatusColor(section.status)}`}>
                        <span className="material-symbols-outlined">
                          {getSectionIcon(section.type)}
                        </span>
                      </div>
                      <div>
                        <h4 className="text-white font-medium">{section.title}</h4>
                        <div className="flex items-center gap-2 text-xs">
                          <span className={`${getStatusColor(section.status)} capitalize`}>
                            {section.status}
                          </span>
                          {section.status === 'complete' && (
                            <span className="text-slate-500">
                              • {Math.round(section.confidence * 100)}% confidence
                            </span>
                          )}
                        </div>
                      </div>
                    </div>

                    {section.status === 'generating' && (
                      <div className="w-4 h-4 border-2 border-yellow-400 border-t-transparent rounded-full animate-spin" />
                    )}
                  </div>

                  {/* Section Content */}
                  {section.status === 'generating' ? (
                    <div className="space-y-2">
                      <div className="h-4 bg-white/10 rounded animate-pulse" />
                      <div className="h-4 bg-white/10 rounded animate-pulse w-3/4" />
                      <div className="h-4 bg-white/10 rounded animate-pulse w-1/2" />
                    </div>
                  ) : section.content ? (
                    <p className="text-slate-300 text-sm leading-relaxed">
                      {section.content}
                    </p>
                  ) : (
                    <p className="text-slate-500 text-sm italic">
                      Waiting for more conversation data...
                    </p>
                  )}

                  {/* Confidence Bar */}
                  {section.status === 'complete' && (
                    <div className="mt-3 pt-3 border-t border-white/5">
                      <div className="flex items-center justify-between text-xs text-slate-500 mb-1">
                        <span>AI Confidence</span>
                        <span>{Math.round(section.confidence * 100)}%</span>
                      </div>
                      <div className="w-full bg-white/10 rounded-full h-1">
                        <motion.div
                          className="h-1 bg-gradient-primary rounded-full"
                          initial={{ width: 0 }}
                          animate={{ width: `${section.confidence * 100}%` }}
                          transition={{ duration: 1, delay: 0.5 }}
                        />
                      </div>
                    </div>
                  )}
                </motion.div>
              ))}
            </AnimatePresence>

            {/* Generation Status */}
            {isGenerating && (
              <motion.div
                className="text-center py-4 border border-white/10 rounded-xl bg-white/5"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
              >
                <div className="flex items-center justify-center gap-3 text-yellow-400">
                  <div className="w-4 h-4 border-2 border-yellow-400 border-t-transparent rounded-full animate-spin" />
                  <span className="text-sm">Generating report sections...</span>
                </div>
              </motion.div>
            )}
          </div>
        )}
      </div>
    </div>
  )
}

export default ReportPreview
