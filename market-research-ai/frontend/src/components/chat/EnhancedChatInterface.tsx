'use client'

import { useState, useRef, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { useMarketResearchStore, useActiveConversation, useAgentState } from '@/store'
import MessageBubble from './MessageBubble'
import AgentStateDisplay from './AgentStateDisplay'
import ReportPreview from './ReportPreview'
import ChatInput from './ChatInput'

const EnhancedChatInterface = () => {
  const activeConversation = useActiveConversation()
  const agentState = useAgentState()
  const [showReportPreview, setShowReportPreview] = useState(false)
  const [isTyping, setIsTyping] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)
  
  const { 
    addMessage, 
    updateAgentState,
    createConversation,
    createReport 
  } = useMarketResearchStore()

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }, [activeConversation?.messages])

  // Create initial conversation if none exists
  useEffect(() => {
    if (!activeConversation) {
      createConversation('Market Research Chat')
    }
  }, [activeConversation, createConversation])

  const handleSendMessage = async (content: string) => {
    if (!activeConversation || !content.trim()) return

    // Add user message
    addMessage(activeConversation.id, {
      type: 'user',
      content: content.trim()
    })

    // Set agent to thinking state
    updateAgentState({ status: 'thinking' })
    setIsTyping(true)

    // Simulate AI processing (replace with actual AI integration)
    setTimeout(() => {
      // Add AI response
      addMessage(activeConversation.id, {
        type: 'assistant',
        content: generateAIResponse(content),
        agentState: { status: 'idle' }
      })

      // Create or update report based on conversation
      updateReportFromConversation()
      
      updateAgentState({ status: 'idle' })
      setIsTyping(false)
    }, 2000)
  }

  const generateAIResponse = (userMessage: string): string => {
    // Mock AI response - replace with actual AI integration
    const responses = [
      "I'll analyze the Bangkok cafe market for you. Let me gather some insights about the current trends and opportunities in the area.",
      "Based on the market data, I can see several interesting patterns in Bangkok's F&B industry. Let me break this down for you.",
      "Great question! I'm processing the latest market research data for Bangkok's restaurant scene. Here's what I found:",
      "I'll help you understand the competitive landscape in Bangkok's cafe market. Let me compile a comprehensive analysis."
    ]
    return responses[Math.floor(Math.random() * responses.length)]
  }

  const updateReportFromConversation = () => {
    if (!activeConversation) return
    
    // Auto-generate report based on conversation content
    // This would integrate with your AI system to create structured reports
    console.log('Updating report from conversation:', activeConversation.id)
  }

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: { 
      opacity: 1,
      transition: {
        duration: 0.6,
        staggerChildren: 0.1
      }
    }
  }

  const sidebarVariants = {
    hidden: { opacity: 0, x: 300 },
    visible: { 
      opacity: 1, 
      x: 0,
      transition: { duration: 0.4, ease: "easeOut" }
    },
    exit: { 
      opacity: 0, 
      x: 300,
      transition: { duration: 0.3 }
    }
  }

  if (!activeConversation) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="text-center">
          <div className="w-8 h-8 border-2 border-primary-500 border-t-transparent rounded-full animate-spin mx-auto mb-4" />
          <p className="text-slate-400">Initializing chat...</p>
        </div>
      </div>
    )
  }

  return (
    <motion.div 
      className="h-full flex relative"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      {/* Main Chat Area */}
      <div className={`flex-1 flex flex-col transition-all duration-300 ${
        showReportPreview ? 'mr-96' : ''
      }`}>
        {/* Chat Header */}
        <div className="flex items-center justify-between p-6 border-b border-white/10 bg-white/5 backdrop-blur-sm">
          <div className="flex items-center gap-4">
            <div>
              <h2 className="text-xl font-bold text-white">{activeConversation.title}</h2>
              <p className="text-sm text-slate-400">
                {activeConversation.messages.length} messages • Market Research Assistant
              </p>
            </div>
            
            {/* Agent Status */}
            {agentState.status !== 'idle' && (
              <AgentStateDisplay state={agentState} />
            )}
          </div>

          <div className="flex items-center gap-3">
            {/* Report Preview Toggle */}
            <button
              onClick={() => setShowReportPreview(!showReportPreview)}
              className={`btn-secondary p-3 rounded-xl transition-colors ${
                showReportPreview ? 'bg-primary-500/20 text-primary-400' : ''
              }`}
              title="Toggle report preview"
            >
              <span className="material-symbols-outlined">description</span>
            </button>

            {/* Export Chat */}
            <button className="btn-secondary p-3 rounded-xl">
              <span className="material-symbols-outlined">download</span>
            </button>

            {/* New Chat */}
            <button 
              className="btn-primary px-4 py-3 rounded-xl"
              onClick={() => createConversation('New Market Research Chat')}
            >
              <span className="material-symbols-outlined mr-2">add</span>
              New Chat
            </button>
          </div>
        </div>

        {/* Messages Area */}
        <div className="flex-1 overflow-y-auto p-6 space-y-6">
          <AnimatePresence>
            {activeConversation.messages.map((message, index) => (
              <motion.div
                key={message.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ 
                  duration: 0.4,
                  delay: index * 0.1,
                  ease: "easeOut"
                }}
              >
                <MessageBubble message={message} />
              </motion.div>
            ))}
          </AnimatePresence>

          {/* Typing Indicator */}
          <AnimatePresence>
            {isTyping && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                className="flex items-center gap-3 text-slate-400"
              >
                <div className="w-8 h-8 rounded-full bg-gradient-primary flex items-center justify-center">
                  <span className="material-symbols-outlined text-white text-sm">smart_toy</span>
                </div>
                <div className="flex items-center gap-1">
                  <div className="w-2 h-2 bg-slate-400 rounded-full animate-bounce" style={{ animationDelay: '0ms' }} />
                  <div className="w-2 h-2 bg-slate-400 rounded-full animate-bounce" style={{ animationDelay: '150ms' }} />
                  <div className="w-2 h-2 bg-slate-400 rounded-full animate-bounce" style={{ animationDelay: '300ms' }} />
                </div>
                <span className="text-sm">AI is thinking...</span>
              </motion.div>
            )}
          </AnimatePresence>

          <div ref={messagesEndRef} />
        </div>

        {/* Chat Input */}
        <ChatInput 
          onSendMessage={handleSendMessage}
          disabled={isTyping}
          placeholder="Ask about Bangkok's cafe and restaurant market..."
        />
      </div>

      {/* Report Preview Sidebar */}
      <AnimatePresence>
        {showReportPreview && (
          <motion.div
            className="absolute right-0 top-0 bottom-0 w-96 border-l border-white/10"
            variants={sidebarVariants}
            initial="hidden"
            animate="visible"
            exit="exit"
          >
            <ReportPreview 
              conversationId={activeConversation.id}
              onClose={() => setShowReportPreview(false)}
            />
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  )
}

export default EnhancedChatInterface
