'use client'

import { motion } from 'framer-motion'
import { ChatMessage } from '@/store'

interface MessageBubbleProps {
  message: ChatMessage
}

const MessageBubble = ({ message }: MessageBubbleProps) => {
  const formatTime = (date: Date) => {
    return new Date(date).toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  if (message.type === 'user') {
    return (
      <motion.div 
        className="flex items-start justify-end gap-4"
        initial={{ opacity: 0, x: 20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.4, ease: "easeOut" }}
      >
        <div className="flex flex-col items-end gap-1">
          <p className="text-xs font-medium text-text-muted">You</p>
          <motion.div 
            className="glass rounded-2xl gradient-primary px-4 py-3 text-sm text-white max-w-xs lg:max-w-md shadow-glow"
            whileHover={{ scale: 1.02 }}
            transition={{ duration: 0.2 }}
          >
            <div className="whitespace-pre-wrap">{message.content}</div>
          </motion.div>
          <p className="text-xs text-text-muted">{formatTime(message.timestamp)}</p>
        </div>
        <motion.div 
          className="w-10 h-10 shrink-0 rounded-full bg-gradient-secondary shadow-glow flex items-center justify-center text-white font-medium"
          whileHover={{ scale: 1.1 }}
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ delay: 0.2, type: "spring", bounce: 0.5 }}
        >
          <span className="material-symbols-outlined text-lg">person</span>
        </motion.div>
      </motion.div>
    )
  }

  return (
    <motion.div 
      className="flex items-start gap-4"
      initial={{ opacity: 0, x: -20 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ duration: 0.4, ease: "easeOut" }}
    >
      <motion.div 
        className="w-10 h-10 shrink-0 rounded-full bg-gradient-primary shadow-glow flex items-center justify-center"
        whileHover={{ scale: 1.1 }}
        initial={{ scale: 0 }}
        animate={{ scale: 1 }}
        transition={{ delay: 0.2, type: "spring", bounce: 0.5 }}
      >
        <motion.span 
          className="material-symbols-outlined text-white text-lg"
          animate={{ rotate: [0, 360] }}
          transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
        >
          smart_toy
        </motion.span>
      </motion.div>
      
      <div className="flex flex-col items-start gap-1 flex-1">
        <p className="text-xs font-medium text-text-muted">AI Assistant</p>
        <motion.div 
          className="glass rounded-2xl border border-white/10 px-4 py-3 text-sm text-white max-w-full shadow-glass"
          whileHover={{ scale: 1.01 }}
          transition={{ duration: 0.2 }}
        >
          <div className="whitespace-pre-wrap leading-relaxed">{message.content}</div>
          
          {/* Tool usage indicators */}
          {message.tools && message.tools.length > 0 && (
            <motion.div 
              className="mt-3 pt-3 border-t border-white/10"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
            >
              <div className="flex items-center flex-wrap gap-2 text-xs text-text-tertiary">
                <div className="flex items-center space-x-1">
                  <motion.span 
                    className="material-symbols-outlined text-sm text-primary-400"
                    animate={{ rotate: [0, 180, 360] }}
                    transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
                  >
                    build
                  </motion.span>
                  <span className="font-medium">Tools used:</span>
                </div>
                <div className="flex flex-wrap gap-1">
                  {message.tools.map((tool, index) => (
                    <motion.span 
                      key={index} 
                      className="glass px-2 py-1 rounded-full text-xs text-primary-300 border border-primary-400/20"
                      initial={{ opacity: 0, scale: 0.8 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ delay: 0.4 + index * 0.1 }}
                      whileHover={{ scale: 1.05 }}
                    >
                      {tool}
                    </motion.span>
                  ))}
                </div>
              </div>
            </motion.div>
          )}
        </motion.div>
        <p className="text-xs text-text-muted">{formatTime(message.timestamp)}</p>
      </div>
    </motion.div>
  )
}

export default MessageBubble