'use client'

import { useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { useMarketResearchStore, useNotifications } from '@/store'

const NotificationCenter = () => {
  const notifications = useNotifications()
  const { removeNotification, markNotificationRead } = useMarketResearchStore()

  // Auto-remove notifications after 5 seconds
  useEffect(() => {
    notifications.forEach(notification => {
      if (!notification.read) {
        const timer = setTimeout(() => {
          removeNotification(notification.id)
        }, 5000)

        return () => clearTimeout(timer)
      }
    })
  }, [notifications, removeNotification])

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'success': return 'check_circle'
      case 'error': return 'error'
      case 'warning': return 'warning'
      case 'info': return 'info'
      default: return 'notifications'
    }
  }

  const getNotificationColor = (type: string) => {
    switch (type) {
      case 'success': return 'border-green-500 bg-green-500/10 text-green-400'
      case 'error': return 'border-red-500 bg-red-500/10 text-red-400'
      case 'warning': return 'border-yellow-500 bg-yellow-500/10 text-yellow-400'
      case 'info': return 'border-blue-500 bg-blue-500/10 text-blue-400'
      default: return 'border-slate-500 bg-slate-500/10 text-slate-400'
    }
  }

  return (
    <div className="fixed top-4 right-4 z-[100] space-y-2 pointer-events-none">
      <AnimatePresence>
        {notifications.map((notification) => (
          <motion.div
            key={notification.id}
            className={`glass-panel border rounded-xl p-4 max-w-sm pointer-events-auto ${getNotificationColor(notification.type)}`}
            initial={{ opacity: 0, x: 300, scale: 0.8 }}
            animate={{ opacity: 1, x: 0, scale: 1 }}
            exit={{ opacity: 0, x: 300, scale: 0.8 }}
            transition={{ 
              type: "spring", 
              stiffness: 300, 
              damping: 30,
              duration: 0.4 
            }}
            layout
          >
            <div className="flex items-start gap-3">
              {/* Icon */}
              <div className="flex-shrink-0 mt-0.5">
                <span className="material-symbols-outlined text-lg">
                  {getNotificationIcon(notification.type)}
                </span>
              </div>

              {/* Content */}
              <div className="flex-1 min-w-0">
                <h4 className="text-sm font-semibold text-white mb-1">
                  {notification.title}
                </h4>
                <p className="text-xs text-slate-300 leading-relaxed">
                  {notification.message}
                </p>
                
                {/* Actions */}
                {notification.actions && notification.actions.length > 0 && (
                  <div className="flex gap-2 mt-3">
                    {notification.actions.map((action, index) => (
                      <button
                        key={index}
                        onClick={() => {
                          action.action()
                          removeNotification(notification.id)
                        }}
                        className={`text-xs px-3 py-1 rounded-lg transition-colors ${
                          action.style === 'primary' 
                            ? 'bg-primary-500 text-white hover:bg-primary-600'
                            : action.style === 'danger'
                            ? 'bg-red-500 text-white hover:bg-red-600'
                            : 'bg-white/10 text-white hover:bg-white/20'
                        }`}
                      >
                        {action.label}
                      </button>
                    ))}
                  </div>
                )}
              </div>

              {/* Close button */}
              <button
                onClick={() => removeNotification(notification.id)}
                className="flex-shrink-0 text-slate-400 hover:text-white transition-colors p-1 rounded-lg hover:bg-white/10"
              >
                <span className="material-symbols-outlined text-sm">close</span>
              </button>
            </div>

            {/* Progress bar for auto-dismiss */}
            <motion.div
              className="absolute bottom-0 left-0 h-0.5 bg-current opacity-30 rounded-b-xl"
              initial={{ width: '100%' }}
              animate={{ width: '0%' }}
              transition={{ duration: 5, ease: "linear" }}
            />
          </motion.div>
        ))}
      </AnimatePresence>
    </div>
  )
}

export default NotificationCenter
