'use client'

import { motion } from 'framer-motion'
import { useMarketResearchStore } from '@/store'

const Header = () => {
  const activeView = useMarketResearchStore((state) => state.activeView)
  const setActiveView = useMarketResearchStore((state) => state.setActiveView)
  const currentReport = useMarketResearchStore((state) => {
    const { list, activeReportId } = state.reports
    return list.find(r => r.id === activeReportId) || null
  })

  const containerVariants = {
    hidden: { opacity: 0, y: -20 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: {
        duration: 0.6,
        ease: "easeOut",
        staggerChildren: 0.1
      }
    }
  }

  const itemVariants = {
    hidden: { opacity: 0, y: -10 },
    visible: { opacity: 1, y: 0 }
  }

  const viewData = {
    chat: {
      title: 'AI Chat & Research',
      subtitle: "AI-powered market research for Bangkok's cafe & restaurant industry"
    },
    report: {
      title: currentReport?.title || 'Market Research Report',
      subtitle: 'Professional market research reporting with AI enhancement'
    }
  }

  // Ensure we have valid viewData for the current activeView
  const currentViewData = viewData[activeView] || viewData.chat

  return (
    <motion.header 
      className="glass sticky top-0 z-50 border-b border-white/10 backdrop-blur-3xl"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      <div className="flex items-center justify-between px-8 py-4">
        {/* Logo and Title Section */}
        <motion.div 
          className="flex items-center space-x-6"
          variants={itemVariants}
        >
          {/* Animated Logo */}
          <motion.div 
            className="flex items-center space-x-3"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <div className="relative">
              <motion.div 
                className="w-10 h-10 gradient-primary rounded-2xl flex items-center justify-center shadow-glow"
                animate={{ 
                  boxShadow: [
                    "0 0 20px rgba(99, 102, 241, 0.3)",
                    "0 0 30px rgba(139, 92, 246, 0.5)",
                    "0 0 20px rgba(99, 102, 241, 0.3)"
                  ]
                }}
                transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
              >
                <svg 
                  width="24" 
                  height="24" 
                  viewBox="0 0 48 48" 
                  fill="none" 
                  xmlns="http://www.w3.org/2000/svg"
                  className="text-white"
                >
                  <motion.path 
                    clipRule="evenodd" 
                    d="M39.475 21.6262C40.358 21.4363 40.6863 21.5589 40.7581 21.5934C40.7876 21.655 40.8547 21.857 40.8082 22.3336C40.7408 23.0255 40.4502 24.0046 39.8572 25.2301C38.6799 27.6631 36.5085 30.6631 33.5858 33.5858C30.6631 36.5085 27.6632 38.6799 25.2301 39.8572C24.0046 40.4502 23.0255 40.7407 22.3336 40.8082C21.8571 40.8547 21.6551 40.7875 21.5934 40.7581C21.5589 40.6863 21.4363 40.358 21.6262 39.475C21.8562 38.4054 22.4689 36.9657 23.5038 35.2817C24.7575 33.2417 26.5497 30.9744 28.7621 28.762C30.9744 26.5497 33.2417 24.7574 35.2817 23.5037C36.9657 22.4689 38.4054 21.8562 39.475 21.6262ZM4.41189 29.2403L18.7597 43.5881C19.8813 44.7097 21.4027 44.9179 22.7217 44.7893C24.0585 44.659 25.5148 44.1631 26.9723 43.4579C29.9052 42.0387 33.2618 39.5667 36.4142 36.4142C39.5667 33.2618 42.0387 29.9052 43.4579 26.9723C44.1631 25.5148 44.659 24.0585 44.7893 22.7217C44.9179 21.4027 44.7097 19.8813 43.5881 18.7597L29.2403 4.41187C27.8527 3.02428 25.8765 3.02573 24.2861 3.36776C22.6081 3.72863 20.7334 4.58419 18.8396 5.74801C16.4978 7.18716 13.9881 9.18353 11.5858 11.5858C9.18354 13.988 7.18717 16.4978 5.74802 18.8396C4.58421 20.7334 3.72865 22.6081 3.36778 24.2861C3.02574 25.8765 3.02429 27.8527 4.41189 29.2403Z" 
                    fill="currentColor" 
                    fillRule="evenodd"
                    initial={{ pathLength: 0 }}
                    animate={{ pathLength: 1 }}
                    transition={{ duration: 2, ease: "easeInOut" }}
                  />
                </svg>
              </motion.div>
              {/* Glow effect */}
              <div className="absolute inset-0 w-10 h-10 gradient-primary rounded-2xl blur-xl opacity-30 animate-pulse"></div>
            </div>
            
            <motion.h1 
              className="text-2xl font-bold gradient-text"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.2 }}
            >
              MarketMind
            </motion.h1>
          </motion.div>

          {/* Dynamic Title */}
          <motion.div 
            className="hidden md:block"
            variants={itemVariants}
          >
            <motion.h2 
              className="text-xl font-semibold text-white"
              key={activeView}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
            >
              {viewData[activeView].title}
            </motion.h2>
            <motion.p 
              className="text-sm text-text-tertiary"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.1 }}
            >
              {viewData[activeView].subtitle}
            </motion.p>
          </motion.div>
        </motion.div>

        {/* Action Buttons */}
        <motion.div 
          className="flex items-center space-x-4"
          variants={itemVariants}
        >
          {/* View Switcher */}
          <div className="glass rounded-2xl p-1 border border-white/10">
            <div className="flex items-center space-x-1">
              {['chat', 'report'].map((view) => (
                <motion.button
                  key={view}
                  onClick={() => setActiveView(view as 'chat' | 'report')}
                  className={`
                    relative px-4 py-2 rounded-xl text-sm font-medium transition-all duration-300
                    ${activeView === view 
                      ? 'text-white shadow-inner-glow' 
                      : 'text-text-tertiary hover:text-white'
                    }
                  `}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  {activeView === view && (
                    <motion.div
                      className="absolute inset-0 gradient-primary rounded-xl"
                      layoutId="activeTab"
                      transition={{ type: "spring", bounce: 0.2, duration: 0.6 }}
                    />
                  )}
                  <span className="relative z-10 capitalize">
                    {view}
                  </span>
                </motion.button>
              ))}
            </div>
          </div>

          {/* Action Buttons */}
          <motion.button 
            className="glass-hover rounded-xl px-4 py-2 text-sm font-medium text-text-secondary hover:text-white transition-all duration-300 flex items-center space-x-2"
            whileHover={{ scale: 1.05, y: -2 }}
            whileTap={{ scale: 0.95 }}
          >
            <span className="material-symbols-outlined text-base">share</span>
            <span className="hidden sm:inline">Share</span>
          </motion.button>

          <motion.button 
            className="gradient-primary rounded-xl px-4 py-2 text-sm font-bold text-white shadow-glow hover:shadow-glow-purple transition-all duration-300 flex items-center space-x-2"
            whileHover={{ scale: 1.05, y: -2 }}
            whileTap={{ scale: 0.95 }}
          >
            <span className="material-symbols-outlined text-base">file_download</span>
            <span className="hidden sm:inline">Export</span>
          </motion.button>

          {/* User Avatar */}
          <motion.div 
            className="w-10 h-10 rounded-full bg-gradient-primary shadow-glow flex items-center justify-center cursor-pointer"
            whileHover={{ scale: 1.1, rotate: 5 }}
            whileTap={{ scale: 0.9 }}
          >
            <span className="material-symbols-outlined text-white text-lg">person</span>
          </motion.div>
        </motion.div>
      </div>
    </motion.header>
  )
}

export default Header