'use client'

import { useState, useRef, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'

interface SearchResult {
  id: string
  type: 'conversation' | 'report' | 'location' | 'message'
  title: string
  subtitle?: string
  icon: string
  data?: any
}

interface SearchBarProps {
  placeholder?: string
  onSearch: (query: string) => void
  onSelect?: (result: SearchResult) => void
  className?: string
}

const SearchBar: React.FC<SearchBarProps> = ({
  placeholder = "Search...",
  onSearch,
  onSelect,
  className = ""
}) => {
  const [query, setQuery] = useState('')
  const [isOpen, setIsOpen] = useState(false)
  const [results, setResults] = useState<SearchResult[]>([])
  const [selectedIndex, setSelectedIndex] = useState(-1)
  const inputRef = useRef<HTMLInputElement>(null)
  const resultsRef = useRef<HTMLDivElement>(null)

  // Mock search results - in real app, this would come from your search service
  const mockResults: SearchResult[] = [
    {
      id: '1',
      type: 'conversation',
      title: 'Bangkok Cafe Market Analysis',
      subtitle: 'Discussion about market trends in Sukhumvit area',
      icon: 'chat'
    },
    {
      id: '2',
      type: 'report',
      title: 'Q4 Restaurant Performance Report',
      subtitle: 'Comprehensive analysis of restaurant performance',
      icon: 'description'
    },
    {
      id: '3',
      type: 'location',
      title: 'Thonglor District',
      subtitle: 'High-end dining and cafe concentration area',
      icon: 'location_on'
    },
    {
      id: '4',
      type: 'message',
      title: 'Competitor analysis for coffee shops',
      subtitle: 'AI Assistant • 2 hours ago',
      icon: 'smart_toy'
    }
  ]

  useEffect(() => {
    if (query.length > 0) {
      // Simulate search delay
      const timer = setTimeout(() => {
        const filtered = mockResults.filter(result =>
          result.title.toLowerCase().includes(query.toLowerCase()) ||
          result.subtitle?.toLowerCase().includes(query.toLowerCase())
        )
        setResults(filtered)
        setIsOpen(true)
        setSelectedIndex(-1)
      }, 150)

      return () => clearTimeout(timer)
    } else {
      setResults([])
      setIsOpen(false)
    }
  }, [query])

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    setQuery(value)
    onSearch(value)
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!isOpen || results.length === 0) return

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault()
        setSelectedIndex(prev => 
          prev < results.length - 1 ? prev + 1 : 0
        )
        break
      case 'ArrowUp':
        e.preventDefault()
        setSelectedIndex(prev => 
          prev > 0 ? prev - 1 : results.length - 1
        )
        break
      case 'Enter':
        e.preventDefault()
        if (selectedIndex >= 0) {
          handleSelect(results[selectedIndex])
        }
        break
      case 'Escape':
        setIsOpen(false)
        setSelectedIndex(-1)
        inputRef.current?.blur()
        break
    }
  }

  const handleSelect = (result: SearchResult) => {
    setQuery(result.title)
    setIsOpen(false)
    setSelectedIndex(-1)
    onSelect?.(result)
    inputRef.current?.blur()
  }

  const handleClickOutside = (e: MouseEvent) => {
    if (
      resultsRef.current && 
      !resultsRef.current.contains(e.target as Node) &&
      !inputRef.current?.contains(e.target as Node)
    ) {
      setIsOpen(false)
      setSelectedIndex(-1)
    }
  }

  useEffect(() => {
    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  const getResultTypeColor = (type: string) => {
    switch (type) {
      case 'conversation': return 'text-blue-400'
      case 'report': return 'text-green-400'
      case 'location': return 'text-orange-400'
      case 'message': return 'text-purple-400'
      default: return 'text-slate-400'
    }
  }

  return (
    <div className={`relative ${className}`}>
      {/* Search Input */}
      <div className="relative">
        <motion.div
          className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400"
          animate={{ scale: query ? 0.9 : 1 }}
          transition={{ duration: 0.2 }}
        >
          <span className="material-symbols-outlined text-lg">search</span>
        </motion.div>
        
        <input
          ref={inputRef}
          type="text"
          value={query}
          onChange={handleInputChange}
          onKeyDown={handleKeyDown}
          onFocus={() => query && setIsOpen(true)}
          placeholder={placeholder}
          className="w-full pl-10 pr-4 py-3 bg-white/5 border border-white/10 rounded-xl text-white placeholder-slate-400 focus:outline-none focus:border-primary-500 focus:bg-white/10 transition-all duration-200"
        />

        {/* Clear button */}
        <AnimatePresence>
          {query && (
            <motion.button
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-slate-400 hover:text-white transition-colors"
              onClick={() => {
                setQuery('')
                setIsOpen(false)
                onSearch('')
                inputRef.current?.focus()
              }}
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.8 }}
              transition={{ duration: 0.2 }}
            >
              <span className="material-symbols-outlined text-lg">close</span>
            </motion.button>
          )}
        </AnimatePresence>
      </div>

      {/* Search Results */}
      <AnimatePresence>
        {isOpen && results.length > 0 && (
          <motion.div
            ref={resultsRef}
            className="absolute top-full left-0 right-0 mt-2 glass-panel border border-white/10 rounded-xl shadow-xl z-50 overflow-hidden"
            initial={{ opacity: 0, y: -10, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: -10, scale: 0.95 }}
            transition={{ duration: 0.2 }}
          >
            <div className="p-2">
              <div className="text-xs text-slate-400 px-3 py-2 border-b border-white/5">
                {results.length} result{results.length !== 1 ? 's' : ''} found
              </div>
              
              <div className="max-h-80 overflow-y-auto">
                {results.map((result, index) => (
                  <motion.div
                    key={result.id}
                    className={`flex items-center gap-3 p-3 rounded-lg cursor-pointer transition-all duration-200 ${
                      index === selectedIndex 
                        ? 'bg-primary-500/20 border border-primary-500/30' 
                        : 'hover:bg-white/5'
                    }`}
                    onClick={() => handleSelect(result)}
                    onMouseEnter={() => setSelectedIndex(index)}
                    whileHover={{ x: 2 }}
                    transition={{ duration: 0.1 }}
                  >
                    <div className={`${getResultTypeColor(result.type)}`}>
                      <span className="material-symbols-outlined text-lg">
                        {result.icon}
                      </span>
                    </div>
                    
                    <div className="flex-1 min-w-0">
                      <div className="text-sm font-medium text-white truncate">
                        {result.title}
                      </div>
                      {result.subtitle && (
                        <div className="text-xs text-slate-400 truncate">
                          {result.subtitle}
                        </div>
                      )}
                    </div>
                    
                    <div className="text-xs text-slate-500 capitalize">
                      {result.type}
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* No results */}
      <AnimatePresence>
        {isOpen && query && results.length === 0 && (
          <motion.div
            className="absolute top-full left-0 right-0 mt-2 glass-panel border border-white/10 rounded-xl shadow-xl z-50 p-6 text-center"
            initial={{ opacity: 0, y: -10, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: -10, scale: 0.95 }}
            transition={{ duration: 0.2 }}
          >
            <span className="material-symbols-outlined text-3xl text-slate-400 mb-2 block">
              search_off
            </span>
            <p className="text-slate-400 text-sm">
              No results found for "{query}"
            </p>
            <p className="text-slate-500 text-xs mt-1">
              Try different keywords or check your spelling
            </p>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}

export default SearchBar
