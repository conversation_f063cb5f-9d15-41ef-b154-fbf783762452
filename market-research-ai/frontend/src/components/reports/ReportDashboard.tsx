'use client'

import { motion } from 'framer-motion'
import { useMarketResearchStore, useReports, useActiveReport } from '@/store'

const ReportDashboard = () => {
  const reports = useReports()
  const activeReport = useActiveReport()
  const { createReport, setActiveReport } = useMarketResearchStore()

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: { 
      opacity: 1,
      transition: {
        duration: 0.6,
        staggerChildren: 0.1
      }
    }
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: { duration: 0.4, ease: "easeOut" }
    }
  }

  const handleCreateReport = () => {
    createReport('New Market Analysis Report')
  }

  if (reports.list.length === 0) {
    return (
      <div className="h-full flex items-center justify-center p-6">
        <motion.div 
          className="text-center"
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          <motion.div 
            className="w-20 h-20 mx-auto mb-6 rounded-3xl glass border border-white/10 shadow-glass flex items-center justify-center"
            variants={itemVariants}
            whileHover={{ scale: 1.05, rotate: 5 }}
          >
            <span className="material-symbols-outlined text-3xl text-primary-400">description</span>
          </motion.div>
          
          <motion.h3 
            className="text-xl font-bold text-white mb-3"
            variants={itemVariants}
          >
            No Reports Yet
          </motion.h3>
          
          <motion.p 
            className="text-slate-400 mb-6 max-w-md"
            variants={itemVariants}
          >
            Create your first market research report to get started with AI-powered analysis
          </motion.p>
          
          <motion.button
            className="btn-primary px-6 py-3 rounded-xl"
            variants={itemVariants}
            whileHover={{ scale: 1.05, y: -2 }}
            whileTap={{ scale: 0.95 }}
            onClick={handleCreateReport}
          >
            <span className="material-symbols-outlined mr-2">add</span>
            Create Report
          </motion.button>
        </motion.div>
      </div>
    )
  }

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="p-6 border-b border-white/10">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold text-white">Reports</h3>
            <p className="text-sm text-slate-400">{reports.list.length} report{reports.list.length !== 1 ? 's' : ''}</p>
          </div>
          
          <button
            onClick={handleCreateReport}
            className="btn-primary px-4 py-2 rounded-lg text-sm"
          >
            <span className="material-symbols-outlined mr-2 text-base">add</span>
            New Report
          </button>
        </div>
      </div>

      {/* Reports List */}
      <div className="flex-1 overflow-y-auto p-6 space-y-4">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          {reports.list.map((report) => (
            <motion.div
              key={report.id}
              className={`glass-panel p-4 rounded-xl cursor-pointer transition-all duration-200 ${
                activeReport?.id === report.id 
                  ? 'border-primary-500 bg-primary-500/10' 
                  : 'border-white/10 hover:border-white/20 hover:bg-white/5'
              }`}
              variants={itemVariants}
              whileHover={{ scale: 1.02, y: -2 }}
              whileTap={{ scale: 0.98 }}
              onClick={() => setActiveReport(report.id)}
            >
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <h4 className="text-white font-medium mb-1">{report.title}</h4>
                  {report.description && (
                    <p className="text-slate-400 text-sm mb-2">{report.description}</p>
                  )}
                  
                  <div className="flex items-center gap-4 text-xs text-slate-500">
                    <span>Updated {report.updatedAt.toLocaleDateString()}</span>
                    <span>{report.content.length} blocks</span>
                    <span>v{report.version}</span>
                  </div>
                </div>
                
                <div className="flex items-center gap-2">
                  {report.shared && (
                    <span className="material-symbols-outlined text-green-400 text-sm">share</span>
                  )}
                  <span className="material-symbols-outlined text-slate-400">chevron_right</span>
                </div>
              </div>
            </motion.div>
          ))}
        </motion.div>
      </div>

      {/* Quick Actions */}
      <div className="p-6 border-t border-white/10">
        <div className="grid grid-cols-2 gap-3">
          <button className="btn-secondary text-sm py-2 px-3 rounded-lg">
            <span className="material-symbols-outlined mr-2 text-base">upload</span>
            Import
          </button>
          <button className="btn-secondary text-sm py-2 px-3 rounded-lg">
            <span className="material-symbols-outlined mr-2 text-base">download</span>
            Export All
          </button>
        </div>
      </div>
    </div>
  )
}

export default ReportDashboard
