'use client'

import { useState } from 'react'
import { ReportBlock as ReportBlockType, useMarketResearchStore } from '@/store'

interface ReportBlockProps {
  block: ReportBlockType
  reportId: string
  isLast: boolean
}

const ReportBlock = ({ block, reportId, isLast }: ReportBlockProps) => {
  const { updateReportBlock, deleteReportBlock } = useMarketResearchStore()
  const [isEditing, setIsEditing] = useState(false)
  const [hovering, setHovering] = useState(false)

  const handleUpdate = (updates: Partial<ReportBlockType>) => {
    updateReportBlock(reportId, block.id, updates)
  }

  const handleDelete = () => {
    deleteReportBlock(reportId, block.id)
  }

  const renderContent = () => {
    switch (block.type) {
      case 'heading':
        return isEditing ? (
          <input
            type="text"
            value={block.content}
            onChange={(e) => handleUpdate({ content: e.target.value })}
            onBlur={() => setIsEditing(false)}
            onKeyPress={(e) => e.key === 'Enter' && setIsEditing(false)}
            className="text-3xl font-bold text-white bg-transparent border-none focus:outline-none focus:ring-0 w-full"
            autoFocus
          />
        ) : (
          <h2 
            className="text-3xl font-bold text-white mt-12 mb-4 cursor-pointer hover:text-gray-200"
            onClick={() => setIsEditing(true)}
          >
            {block.content}
          </h2>
        )

      case 'text':
        return isEditing ? (
          <textarea
            value={block.content}
            onChange={(e) => handleUpdate({ content: e.target.value })}
            onBlur={() => setIsEditing(false)}
            className="w-full text-gray-300 bg-transparent border-none focus:outline-none focus:ring-0 resize-none min-h-[60px]"
            autoFocus
          />
        ) : (
          <p 
            className="text-gray-300 leading-relaxed cursor-text hover:bg-gray-800/30 rounded p-2 -m-2"
            onClick={() => setIsEditing(true)}
          >
            {block.content}
          </p>
        )

      case 'list':
        return (
          <ul className="space-y-2">
            {Array.isArray(block.content) ? block.content.map((item: string, index: number) => (
              <li key={index} className="flex items-start space-x-4">
                <span className="material-symbols-outlined mt-1 text-secondary">grain</span>
                {isEditing ? (
                  <input
                    type="text"
                    value={item}
                    onChange={(e) => {
                      const newContent = [...block.content]
                      newContent[index] = e.target.value
                      handleUpdate({ content: newContent })
                    }}
                    className="flex-1 text-gray-300 bg-transparent border-none focus:outline-none focus:ring-0"
                  />
                ) : (
                  <span 
                    className="flex-1 text-gray-300 cursor-text hover:bg-gray-800/30 rounded p-1 -m-1"
                    onClick={() => setIsEditing(true)}
                  >
                    {item}
                  </span>
                )}
              </li>
            )) : null}
          </ul>
        )

      case 'quote':
        return isEditing ? (
          <textarea
            value={block.content}
            onChange={(e) => handleUpdate({ content: e.target.value })}
            onBlur={() => setIsEditing(false)}
            className="w-full border-l-4 border-secondary pl-4 my-6 italic text-gray-300 bg-transparent border-none focus:outline-none focus:ring-0 resize-none"
            autoFocus
          />
        ) : (
          <blockquote 
            className="border-l-4 border-secondary pl-4 my-6 italic text-gray-300 cursor-text hover:bg-gray-800/30 rounded p-2 -m-2"
            onClick={() => setIsEditing(true)}
          >
            {block.content}
          </blockquote>
        )

      case 'chart':
        return (
          <div className="my-6 p-4 bg-gray-900 rounded-lg border border-gray-700">
            <div className="aspect-video bg-gray-800 rounded flex items-center justify-center text-gray-400">
              <div className="text-center">
                <span className="material-symbols-outlined text-4xl mb-2 block">bar_chart</span>
                <p>Chart Visualization</p>
                <p className="text-sm text-gray-500 mt-1">
                  {block.content?.type || 'bar'} chart with {block.content?.data?.datasets?.[0]?.data?.length || 0} data points
                </p>
              </div>
            </div>
          </div>
        )

      case 'ai-suggestion':
        return (
          <div className="my-6 p-4 bg-secondary/10 border border-secondary rounded-lg">
            <div className="flex items-start space-x-3">
              <span className="material-symbols-outlined text-secondary mt-1">lightbulb</span>
              <div className="flex-1">
                <h4 className="font-bold text-secondary mb-2">AI Suggestion</h4>
                <p className="text-gray-300 mb-3">{block.content}</p>
                <div className="space-x-2">
                  <button className="px-3 py-1 text-sm font-medium text-secondary bg-white border border-secondary rounded-md hover:bg-secondary-50">
                    Accept
                  </button>
                  <button 
                    onClick={handleDelete}
                    className="px-3 py-1 text-sm font-medium text-gray-300 bg-gray-800 border border-gray-700 rounded-md hover:bg-gray-700"
                  >
                    Dismiss
                  </button>
                </div>
              </div>
            </div>
          </div>
        )

      default:
        return <div className="text-gray-400">Unknown block type</div>
    }
  }

  return (
    <div 
      className="relative group"
      onMouseEnter={() => setHovering(true)}
      onMouseLeave={() => setHovering(false)}
    >
      {/* Drag handle and controls */}
      {hovering && block.type !== 'ai-suggestion' && (
        <div className="absolute -left-12 top-1/2 -translate-y-1/2 flex flex-col space-y-1">
          <button className="p-1 text-gray-400 hover:text-secondary">
            <span className="material-symbols-outlined text-sm">drag_indicator</span>
          </button>
          <button 
            onClick={handleDelete}
            className="p-1 text-gray-400 hover:text-red-400"
          >
            <span className="material-symbols-outlined text-sm">delete</span>
          </button>
        </div>
      )}

      {renderContent()}
    </div>
  )
}

export default ReportBlock