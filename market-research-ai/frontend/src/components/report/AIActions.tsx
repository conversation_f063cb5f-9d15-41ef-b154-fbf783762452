'use client'

import { motion } from 'framer-motion'
import { useMarketResearchStore } from '@/store'

interface AIActionsProps {
  reportId: string
}

const AIActions = ({ reportId }: AIActionsProps) => {
  const { addReportBlock } = useMarketResearchStore()

  const actions = [
    {
      id: 'improve',
      label: 'Improve Writing',
      icon: 'spellcheck',
      suggestion: 'Consider adding more specific data points and statistics to strengthen your analysis. Include recent market research findings and cite credible sources.',
      color: 'from-blue-400 to-cyan-500',
      glowColor: 'shadow-blue-500/30'
    },
    {
      id: 'rephrase',
      label: 'Rephrase',
      icon: 'edit_note',
      suggestion: 'The current content could be made more engaging by using active voice and industry-specific terminology that resonates with stakeholders.',
      color: 'from-green-400 to-emerald-500',
      glowColor: 'shadow-green-500/30'
    },
    {
      id: 'summarize',
      label: 'Summarize',
      icon: 'compress',
      suggestion: 'Key takeaways: 1) Bangkok\'s F&B market shows 15% growth YoY, 2) Sukhumvit area has highest cafe density, 3) Local preferences favor specialty coffee and fusion cuisine.',
      color: 'from-orange-400 to-red-500',
      glowColor: 'shadow-orange-500/30'
    },
    {
      id: 'generate',
      label: 'Generate Content',
      icon: 'auto_awesome',
      suggestion: 'Generate a competitive analysis section comparing market leaders like Starbucks, True Coffee, and local independent cafes in terms of pricing, location strategy, and customer segments.',
      color: 'from-purple-400 to-pink-500',
      glowColor: 'shadow-purple-500/30'
    }
  ]

  const handleAIAction = (action: typeof actions[0]) => {
    addReportBlock(reportId, {
      type: 'ai-suggestion',
      content: action.suggestion
    })
  }

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: { 
      opacity: 1,
      transition: {
        duration: 0.6,
        staggerChildren: 0.1
      }
    }
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 20, scale: 0.9 },
    visible: { 
      opacity: 1, 
      y: 0, 
      scale: 1,
      transition: { duration: 0.4, ease: "easeOut" }
    }
  }

  return (
    <motion.div 
      className="relative group my-12"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      {/* Decorative line with icon */}
      <motion.div 
        className="flex items-center space-x-4 text-sm text-text-tertiary mb-8"
        variants={itemVariants}
      >
        <div className="flex-1 h-px bg-gradient-to-r from-transparent via-white/20 to-transparent"></div>
        <motion.div 
          className="flex items-center space-x-2 glass px-4 py-2 rounded-full border border-white/10"
          whileHover={{ scale: 1.05 }}
        >
          <motion.span 
            className="material-symbols-outlined text-primary-400"
            animate={{ rotate: [0, 360] }}
            transition={{ duration: 3, repeat: Infinity, ease: "linear" }}
          >
            spark
          </motion.span>
          <span className="font-medium">AI Actions</span>
        </motion.div>
        <div className="flex-1 h-px bg-gradient-to-r from-transparent via-white/20 to-transparent"></div>
      </motion.div>
      
      {/* Action buttons */}
      <motion.div 
        className="flex flex-wrap justify-center gap-4"
        variants={containerVariants}
      >
        {actions.map((action, index) => (
          <motion.button
            key={action.id}
            onClick={() => handleAIAction(action)}
            className={`
              group/btn glass-hover relative overflow-hidden rounded-2xl px-6 py-3 text-sm font-medium text-white 
              transition-all duration-300 flex items-center space-x-3 border border-white/10
              ${action.id === 'generate' 
                ? 'bg-gradient-to-r from-primary-500 to-secondary-500 shadow-glow' 
                : 'hover:shadow-lg'
              }
            `}
            variants={itemVariants}
            whileHover={{ 
              scale: 1.05, 
              y: -2,
              transition: { duration: 0.2 }
            }}
            whileTap={{ scale: 0.95 }}
          >
            {/* Animated background for generate button */}
            {action.id === 'generate' && (
              <motion.div
                className="absolute inset-0 bg-gradient-to-r from-primary-400 to-secondary-400"
                animate={{
                  opacity: [0.8, 1, 0.8],
                  scale: [1, 1.02, 1]
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  ease: "easeInOut"
                }}
              />
            )}
            
            {/* Icon with rotation animation */}
            <motion.span 
              className={`material-symbols-outlined text-lg relative z-10 ${
                action.id === 'generate' ? 'text-white' : 'text-text-secondary group-hover/btn:text-white'
              }`}
              whileHover={{ rotate: 15 }}
              transition={{ duration: 0.2 }}
            >
              {action.icon}
            </motion.span>
            
            {/* Label */}
            <span className={`relative z-10 ${
              action.id === 'generate' ? 'text-white' : 'text-text-secondary group-hover/btn:text-white'
            }`}>
              {action.label}
            </span>

            {/* Hover effect overlay */}
            <motion.div
              className={`absolute inset-0 bg-gradient-to-r ${action.color} opacity-0 group-hover/btn:opacity-20 transition-opacity duration-300`}
              initial={false}
            />
          </motion.button>
        ))}
      </motion.div>

      {/* Floating particles effect */}
      <div className="absolute inset-0 pointer-events-none overflow-hidden">
        {[...Array(6)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-1 h-1 bg-primary-400 rounded-full opacity-30"
            animate={{
              x: [0, Math.random() * 400 - 200],
              y: [0, Math.random() * 200 - 100],
              opacity: [0, 0.6, 0],
              scale: [0, 1, 0]
            }}
            transition={{
              duration: 4 + Math.random() * 2,
              repeat: Infinity,
              delay: i * 0.5,
              ease: "easeInOut"
            }}
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`
            }}
          />
        ))}
      </div>
    </motion.div>
  )
}

export default AIActions