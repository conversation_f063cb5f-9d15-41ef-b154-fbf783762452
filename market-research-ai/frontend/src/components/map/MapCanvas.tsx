'use client'

import { useEffect, useRef, useState } from 'react'
import mapboxgl from 'mapbox-gl'
import { useMarketResearchStore } from '@/store'

// Set your Mapbox access token
mapboxgl.accessToken = 'pk.eyJ1Ijoia2hpd25pdGkiLCJhIjoiY205eDFwMzl0MHY1YzJscjB3bm4xcnh5ZyJ9.ANGVE0tiA9NslBn8ft_9fQ'

const MapCanvas = () => {
  const mapContainer = useRef<HTMLDivElement>(null)
  const map = useRef<mapboxgl.Map | null>(null)
  const { mapState, updateMapState } = useMarketResearchStore()
  const [activeLayer, setActiveLayer] = useState<string>('restaurants')

  useEffect(() => {
    if (map.current) return // Initialize map only once

    if (mapContainer.current) {
      map.current = new mapboxgl.Map({
        container: mapContainer.current,
        style: 'mapbox://styles/mapbox/dark-v11',
        center: mapState.center,
        zoom: mapState.zoom,
      })

      // Add navigation controls
      map.current.addControl(new mapboxgl.NavigationControl(), 'top-right')

      // Add geolocation control
      map.current.addControl(
        new mapboxgl.GeolocateControl({
          positionOptions: {
            enableHighAccuracy: true
          },
          trackUserLocation: true,
          showUserHeading: true
        }),
        'top-right'
      )

      // Update state when map moves
      map.current.on('move', () => {
        if (map.current) {
          updateMapState({
            center: [map.current.getCenter().lng, map.current.getCenter().lat],
            zoom: map.current.getZoom()
          })
        }
      })

      // Add sample data layers once map loads
      map.current.on('load', () => {
        addDataLayers()
        addSampleMarkers()
      })
    }

    return () => {
      if (map.current) {
        map.current.remove()
      }
    }
  }, [])

  const addDataLayers = () => {
    if (!map.current) return

    // Add sample restaurant density heatmap
    map.current.addSource('restaurant-density', {
      type: 'geojson',
      data: {
        type: 'FeatureCollection',
        features: generateSampleHeatmapData()
      }
    })

    map.current.addLayer({
      id: 'restaurant-heatmap',
      type: 'heatmap',
      source: 'restaurant-density',
      maxzoom: 15,
      paint: {
        'heatmap-weight': ['interpolate', ['linear'], ['get', 'density'], 0, 0, 6, 1],
        'heatmap-intensity': ['interpolate', ['linear'], ['zoom'], 0, 1, 15, 3],
        'heatmap-color': [
          'interpolate',
          ['linear'],
          ['heatmap-density'],
          0, 'rgba(33,102,172,0)',
          0.2, 'rgb(103,169,207)',
          0.4, 'rgb(209,229,240)',
          0.6, 'rgb(253,219,199)',
          0.8, 'rgb(239,138,98)',
          1, 'rgb(178,24,43)'
        ],
        'heatmap-radius': ['interpolate', ['linear'], ['zoom'], 0, 2, 15, 20]
      }
    })

    // Add demographic data layer (sample)
    map.current.addSource('demographics', {
      type: 'geojson',
      data: {
        type: 'FeatureCollection',
        features: generateDemographicData()
      }
    })

    map.current.addLayer({
      id: 'demographics-layer',
      type: 'fill',
      source: 'demographics',
      layout: {},
      paint: {
        'fill-color': [
          'interpolate',
          ['linear'],
          ['get', 'income'],
          20000, '#ffffcc',
          40000, '#a1dab4',
          60000, '#41b6c4',
          80000, '#2c7fb8',
          100000, '#253494'
        ],
        'fill-opacity': 0.6
      }
    })
  }

  const addSampleMarkers = () => {
    if (!map.current) return

    const restaurants = [
      { name: 'Cafe Tartine', coordinates: [100.5018, 13.7563], type: 'cafe' },
      { name: 'Blue Elephant', coordinates: [100.5200, 13.7500], type: 'restaurant' },
      { name: 'Roast Coffee', coordinates: [100.5300, 13.7400], type: 'cafe' },
      { name: 'Siam Tea Room', coordinates: [100.5100, 13.7600], type: 'cafe' },
      { name: 'Bangkok Bistro', coordinates: [100.5400, 13.7350], type: 'restaurant' },
    ]

    restaurants.forEach((restaurant) => {
      const el = document.createElement('div')
      el.className = 'marker'
      el.style.backgroundImage = `url(data:image/svg+xml;base64,${btoa(`
        <svg width="20" height="20" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
          <circle cx="10" cy="10" r="8" fill="${restaurant.type === 'cafe' ? '#6B46C1' : '#9333ea'}" stroke="white" stroke-width="2"/>
          <text x="10" y="14" text-anchor="middle" fill="white" font-size="10" font-family="Arial">${restaurant.type === 'cafe' ? 'C' : 'R'}</text>
        </svg>
      `)})`
      el.style.width = '20px'
      el.style.height = '20px'
      el.style.backgroundSize = 'cover'
      el.style.cursor = 'pointer'

      const popup = new mapboxgl.Popup({ offset: 25 }).setText(restaurant.name)

      new mapboxgl.Marker(el)
        .setLngLat(restaurant.coordinates)
        .setPopup(popup)
        .addTo(map.current!)
    })
  }

  const generateSampleHeatmapData = () => {
    const features = []
    const bangkokDistricts = [
      [100.5018, 13.7563], // Siam
      [100.5400, 13.7350], // Sukhumvit
      [100.5200, 13.7500], // Silom
      [100.5100, 13.7600], // Pratunam
      [100.4900, 13.7450], // Khao San
    ]

    bangkokDistricts.forEach((center) => {
      // Generate random points around each district center
      for (let i = 0; i < 50; i++) {
        const lng = center[0] + (Math.random() - 0.5) * 0.02
        const lat = center[1] + (Math.random() - 0.5) * 0.02
        features.push({
          type: 'Feature',
          geometry: {
            type: 'Point',
            coordinates: [lng, lat]
          },
          properties: {
            density: Math.random() * 6
          }
        })
      }
    })

    return features
  }

  const generateDemographicData = () => {
    return [
      {
        type: 'Feature',
        geometry: {
          type: 'Polygon',
          coordinates: [[
            [100.490, 13.740],
            [100.510, 13.740],
            [100.510, 13.760],
            [100.490, 13.760],
            [100.490, 13.740]
          ]]
        },
        properties: {
          district: 'Pathumwan',
          income: 85000,
          population: 180000
        }
      },
      {
        type: 'Feature',
        geometry: {
          type: 'Polygon',
          coordinates: [[
            [100.510, 13.720],
            [100.550, 13.720],
            [100.550, 13.750],
            [100.510, 13.750],
            [100.510, 13.720]
          ]]
        },
        properties: {
          district: 'Watthana',
          income: 95000,
          population: 220000
        }
      }
    ]
  }

  const toggleLayer = (layerName: string) => {
    if (!map.current) return

    setActiveLayer(layerName)
    
    // Toggle layer visibility
    const layers = {
      'restaurants': 'restaurant-heatmap',
      'demographics': 'demographics-layer'
    }

    Object.entries(layers).forEach(([key, layerId]) => {
      const visibility = key === layerName ? 'visible' : 'none'
      if (map.current!.getLayer(layerId)) {
        map.current!.setLayoutProperty(layerId, 'visibility', visibility)
      }
    })
  }

  return (
    <div className="relative h-full bg-gray-900">
      {/* Map header */}
      <div className="absolute top-0 left-0 right-0 z-10 bg-background-dark/90 backdrop-blur-sm border-b border-gray-700 p-4">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-xl font-bold text-white">Bangkok Market Map</h3>
            <p className="text-sm text-gray-400">Interactive data visualization for F&B market research</p>
          </div>
          
          {/* Layer controls */}
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-400">Layers:</span>
            <button
              onClick={() => toggleLayer('restaurants')}
              className={`px-3 py-1.5 text-sm rounded-md transition-colors ${
                activeLayer === 'restaurants'
                  ? 'bg-primary text-white'
                  : 'bg-gray-800 text-gray-300 hover:bg-gray-700'
              }`}
            >
              Restaurants
            </button>
            <button
              onClick={() => toggleLayer('demographics')}
              className={`px-3 py-1.5 text-sm rounded-md transition-colors ${
                activeLayer === 'demographics'
                  ? 'bg-primary text-white'
                  : 'bg-gray-800 text-gray-300 hover:bg-gray-700'
              }`}
            >
              Demographics
            </button>
          </div>
        </div>
      </div>

      {/* Map container */}
      <div ref={mapContainer} className="w-full h-full" />

      {/* Legend */}
      <div className="absolute bottom-4 left-4 bg-background-dark/90 backdrop-blur-sm rounded-lg p-4 border border-gray-700">
        <h4 className="text-sm font-medium text-white mb-2">Legend</h4>
        {activeLayer === 'restaurants' && (
          <div className="space-y-1">
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 rounded-full bg-primary"></div>
              <span className="text-xs text-gray-300">Cafes</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 rounded-full bg-secondary"></div>
              <span className="text-xs text-gray-300">Restaurants</span>
            </div>
            <div className="text-xs text-gray-400 mt-2">Heat intensity = Restaurant density</div>
          </div>
        )}
        {activeLayer === 'demographics' && (
          <div className="space-y-1">
            <div className="text-xs text-gray-400">Average Income (THB)</div>
            <div className="flex items-center space-x-1">
              <div className="w-3 h-3" style={{backgroundColor: '#ffffcc'}}></div>
              <span className="text-xs text-gray-300">20k</span>
            </div>
            <div className="flex items-center space-x-1">
              <div className="w-3 h-3" style={{backgroundColor: '#41b6c4'}}></div>
              <span className="text-xs text-gray-300">60k</span>
            </div>
            <div className="flex items-center space-x-1">
              <div className="w-3 h-3" style={{backgroundColor: '#253494'}}></div>
              <span className="text-xs text-gray-300">100k+</span>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default MapCanvas