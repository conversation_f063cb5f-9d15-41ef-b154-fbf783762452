"use strict";var _e=Object.create;var Y=Object.defineProperty;var Ie=Object.getOwnPropertyDescriptor;var Ve=Object.getOwnPropertyNames;var We=Object.getPrototypeOf,Ue=Object.prototype.hasOwnProperty;var qe=(e,t)=>{for(var o in t)Y(e,o,{get:t[o],enumerable:!0})},le=(e,t,o,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let r of Ve(t))!Ue.call(e,r)&&r!==o&&Y(e,r,{get:()=>t[r],enumerable:!(n=Ie(t,r))||n.enumerable});return e};var Le=(e,t,o)=>(o=e!=null?_e(We(e)):{},le(t||!e||!e.__esModule?Y(o,"default",{value:e,enumerable:!0}):o,e)),je=e=>le(Y({},"__esModule",{value:!0}),e);var kt={};qe(kt,{Provider:()=>Ee,ReactReduxContext:()=>f,batch:()=>Et,connect:()=>Re,createDispatchHook:()=>pe,createSelectorHook:()=>ae,createStoreHook:()=>j,shallowEqual:()=>R,useDispatch:()=>ke,useSelector:()=>Ae,useStore:()=>G});module.exports=je(kt);var p=Le(require("react"));var $e=p.version.startsWith("19"),Ye=Symbol.for($e?"react.transitional.element":"react.element"),He=Symbol.for("react.portal"),Be=Symbol.for("react.fragment"),ze=Symbol.for("react.strict_mode"),Ke=Symbol.for("react.profiler"),Ge=Symbol.for("react.consumer"),Je=Symbol.for("react.context"),Te=Symbol.for("react.forward_ref"),Xe=Symbol.for("react.suspense"),Ze=Symbol.for("react.suspense_list"),re=Symbol.for("react.memo"),Qe=Symbol.for("react.lazy");var fe=Te,Se=re;function et(e){if(typeof e=="object"&&e!==null){let{$$typeof:t}=e;switch(t){case Ye:switch(e=e.type,e){case Be:case Ke:case ze:case Xe:case Ze:return e;default:switch(e=e&&e.$$typeof,e){case Je:case Te:case Qe:case re:return e;case Ge:return e;default:return t}}case He:return t}}}function ye(e){return et(e)===re}function tt(e,t,o,n,{areStatesEqual:r,areOwnPropsEqual:s,areStatePropsEqual:i}){let u=!1,a,c,d,P,l;function x(S,m){return a=S,c=m,d=e(a,c),P=t(n,c),l=o(d,P,c),u=!0,l}function y(){return d=e(a,c),t.dependsOnOwnProps&&(P=t(n,c)),l=o(d,P,c),l}function M(){return e.dependsOnOwnProps&&(d=e(a,c)),t.dependsOnOwnProps&&(P=t(n,c)),l=o(d,P,c),l}function T(){let S=e(a,c),m=!i(S,d);return d=S,m&&(l=o(d,P,c)),l}function w(S,m){let C=!s(m,c),v=!r(S,a,m,c);return a=S,c=m,C&&v?y():C?M():v?T():l}return function(m,C){return u?w(m,C):x(m,C)}}function ne(e,{initMapStateToProps:t,initMapDispatchToProps:o,initMergeProps:n,...r}){let s=t(e,r),i=o(e,r),u=n(e,r);return tt(s,i,u,e,r)}function se(e,t){let o={};for(let n in e){let r=e[n];typeof r=="function"&&(o[n]=(...s)=>t(r(...s)))}return o}function U(e){return function(o){let n=e(o);function r(){return n}return r.dependsOnOwnProps=!1,r}}function me(e){return e.dependsOnOwnProps?!!e.dependsOnOwnProps:e.length!==1}function H(e,t){return function(n,{displayName:r}){let s=function(u,a){return s.dependsOnOwnProps?s.mapToProps(u,a):s.mapToProps(u,void 0)};return s.dependsOnOwnProps=!0,s.mapToProps=function(u,a){s.mapToProps=e,s.dependsOnOwnProps=me(e);let c=s(u,a);return typeof c=="function"&&(s.mapToProps=c,s.dependsOnOwnProps=me(c),c=s(u,a)),c},s}}function F(e,t){return(o,n)=>{throw new Error(`Invalid value of type ${typeof e} for ${t} argument when connecting component ${n.wrappedComponentName}.`)}}function he(e){return e&&typeof e=="object"?U(t=>se(e,t)):e?typeof e=="function"?H(e,"mapDispatchToProps"):F(e,"mapDispatchToProps"):U(t=>({dispatch:t}))}function we(e){return e?typeof e=="function"?H(e,"mapStateToProps"):F(e,"mapStateToProps"):U(()=>({}))}function ot(e,t,o){return{...o,...e,...t}}function rt(e){return function(o,{displayName:n,areMergedPropsEqual:r}){let s=!1,i;return function(a,c,d){let P=e(a,c,d);return s?r(P,i)||(i=P):(s=!0,i=P),i}}}function xe(e){return e?typeof e=="function"?rt(e):F(e,"mergeProps"):()=>ot}function B(e){e()}function nt(){let e=null,t=null;return{clear(){e=null,t=null},notify(){B(()=>{let o=e;for(;o;)o.callback(),o=o.next})},get(){let o=[],n=e;for(;n;)o.push(n),n=n.next;return o},subscribe(o){let n=!0,r=t={callback:o,next:null,prev:t};return r.prev?r.prev.next=r:e=r,function(){!n||e===null||(n=!1,r.next?r.next.prev=r.prev:t=r.prev,r.prev?r.prev.next=r.next:e=r.next)}}}}var Ce={notify(){},get:()=>[]};function z(e,t){let o,n=Ce,r=0,s=!1;function i(M){d();let T=n.subscribe(M),w=!1;return()=>{w||(w=!0,T(),P())}}function u(){n.notify()}function a(){y.onStateChange&&y.onStateChange()}function c(){return s}function d(){r++,o||(o=t?t.addNestedSub(a):e.subscribe(a),n=nt())}function P(){r--,o&&r===0&&(o(),o=void 0,n.clear(),n=Ce)}function l(){s||(s=!0,d())}function x(){s&&(s=!1,P())}let y={addNestedSub:i,notifyNestedSubs:u,handleChangeWrapper:a,isSubscribed:c,trySubscribe:l,tryUnsubscribe:x,getListeners:()=>n};return y}var st=()=>typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u",pt=st(),at=()=>typeof navigator<"u"&&navigator.product==="ReactNative",ct=at(),it=()=>pt||ct?p.useLayoutEffect:p.useEffect,A=it();function Oe(e,t){return e===t?e!==0||t!==0||1/e===1/t:e!==e&&t!==t}function R(e,t){if(Oe(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;let o=Object.keys(e),n=Object.keys(t);if(o.length!==n.length)return!1;for(let r=0;r<o.length;r++)if(!Object.prototype.hasOwnProperty.call(t,o[r])||!Oe(e[o[r]],t[o[r]]))return!1;return!0}var ut={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},Pt={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},dt={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},Me={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},lt={[fe]:dt,[Se]:Me};function De(e){return ye(e)?Me:lt[e.$$typeof]||ut}var Tt=Object.defineProperty,ft=Object.getOwnPropertyNames,be=Object.getOwnPropertySymbols,St=Object.getOwnPropertyDescriptor,yt=Object.getPrototypeOf,ge=Object.prototype;function q(e,t){if(typeof t!="string"){if(ge){let s=yt(t);s&&s!==ge&&q(e,s)}let o=ft(t);be&&(o=o.concat(be(t)));let n=De(e),r=De(t);for(let s=0;s<o.length;++s){let i=o[s];if(!Pt[i]&&!(r&&r[i])&&!(n&&n[i])){let u=St(t,i);try{Tt(e,i,u)}catch{}}}}return e}var mt=Symbol.for("react-redux-context"),ht=typeof globalThis<"u"?globalThis:{};function wt(){if(!p.createContext)return{};let e=ht[mt]??=new Map,t=e.get(p.createContext);return t||(t=p.createContext(null),e.set(p.createContext,t)),t}var f=wt();var xt=[null,null];function Ct(e,t,o){A(()=>e(...t),o)}function Ot(e,t,o,n,r,s){e.current=n,o.current=!1,r.current&&(r.current=null,s())}function Dt(e,t,o,n,r,s,i,u,a,c,d){if(!e)return()=>{};let P=!1,l=null,x=()=>{if(P||!u.current)return;let M=t.getState(),T,w;try{T=n(M,r.current)}catch(S){w=S,l=S}w||(l=null),T===s.current?i.current||c():(s.current=T,a.current=T,i.current=!0,d())};return o.onStateChange=x,o.trySubscribe(),x(),()=>{if(P=!0,o.tryUnsubscribe(),o.onStateChange=null,l)throw l}}function bt(e,t){return e===t}function gt(e,t,o,{pure:n,areStatesEqual:r=bt,areOwnPropsEqual:s=R,areStatePropsEqual:i=R,areMergedPropsEqual:u=R,forwardRef:a=!1,context:c=f}={}){let d=c,P=we(e),l=he(t),x=xe(o),y=!!e;return T=>{let w=T.displayName||T.name||"Component",S=`Connect(${w})`,m={shouldHandleStateChanges:y,displayName:S,wrappedComponentName:w,WrappedComponent:T,initMapStateToProps:P,initMapDispatchToProps:l,initMergeProps:x,areStatesEqual:r,areStatePropsEqual:i,areOwnPropsEqual:s,areMergedPropsEqual:u};function C(O){let[E,J,b]=p.useMemo(()=>{let{reactReduxForwardedRef:h,...k}=O;return[O.context,h,k]},[O]),_=p.useMemo(()=>{let h=d;return E?.Consumer,h},[E,d]),D=p.useContext(_),I=!!O.store&&!!O.store.getState&&!!O.store.dispatch,ve=!!D&&!!D.store,g=I?O.store:D.store,ce=ve?D.getServerState:g.getState,X=p.useMemo(()=>ne(g.dispatch,m),[g]),[V,ie]=p.useMemo(()=>{if(!y)return xt;let h=z(g,I?void 0:D.subscription),k=h.notifyNestedSubs.bind(h);return[h,k]},[g,I,D]),ue=p.useMemo(()=>I?D:{...D,subscription:V},[I,D,V]),Z=p.useRef(void 0),Q=p.useRef(b),W=p.useRef(void 0),Pe=p.useRef(!1),ee=p.useRef(!1),te=p.useRef(void 0);A(()=>(ee.current=!0,()=>{ee.current=!1}),[]);let de=p.useMemo(()=>()=>W.current&&b===Q.current?W.current:X(g.getState(),b),[g,b]),Ne=p.useMemo(()=>k=>V?Dt(y,g,V,X,Q,Z,Pe,ee,W,ie,k):()=>{},[V]);Ct(Ot,[Q,Z,Pe,b,W,ie]);let $;try{$=p.useSyncExternalStore(Ne,de,ce?()=>X(ce(),b):de)}catch(h){throw te.current&&(h.message+=`
The error may be correlated with this previous error:
${te.current.stack}

`),h}A(()=>{te.current=void 0,W.current=void 0,Z.current=$});let oe=p.useMemo(()=>p.createElement(T,{...$,ref:J}),[J,T,$]);return p.useMemo(()=>y?p.createElement(_.Provider,{value:ue},oe):oe,[_,oe,ue])}let N=p.memo(C);if(N.WrappedComponent=T,N.displayName=C.displayName=S,a){let E=p.forwardRef(function(b,_){return p.createElement(N,{...b,reactReduxForwardedRef:_})});return E.displayName=S,E.WrappedComponent=T,q(E,T)}return q(N,T)}}var Re=gt;function Mt(e){let{children:t,context:o,serverState:n,store:r}=e,s=p.useMemo(()=>{let a=z(r);return{store:r,subscription:a,getServerState:n?()=>n:void 0}},[r,n]),i=p.useMemo(()=>r.getState(),[r]);return A(()=>{let{subscription:a}=s;return a.onStateChange=a.notifyNestedSubs,a.trySubscribe(),i!==r.getState()&&a.notifyNestedSubs(),()=>{a.tryUnsubscribe(),a.onStateChange=void 0}},[s,i]),p.createElement((o||f).Provider,{value:s},t)}var Ee=Mt;function L(e=f){return function(){return p.useContext(e)}}var K=L();function j(e=f){let t=e===f?K:L(e),o=()=>{let{store:n}=t();return n};return Object.assign(o,{withTypes:()=>o}),o}var G=j();function pe(e=f){let t=e===f?G:j(e),o=()=>t().dispatch;return Object.assign(o,{withTypes:()=>o}),o}var ke=pe();var Fe=require("use-sync-external-store/with-selector.js");var Rt=(e,t)=>e===t;function ae(e=f){let t=e===f?K:L(e),o=(n,r={})=>{let{equalityFn:s=Rt}=typeof r=="function"?{equalityFn:r}:r,i=t(),{store:u,subscription:a,getServerState:c}=i,d=p.useRef(!0),P=p.useCallback({[n.name](x){let y=n(x);if(0){if((m==="always"||m==="once"&&d.current)&&!s(y,C))try{}catch(N){}if((S==="always"||S==="once"&&d.current)&&y===x)try{}catch(v){}}return y}}[n.name],[n]),l=(0,Fe.useSyncExternalStoreWithSelector)(a.addNestedSub,u.getState,c||u.getState,P,s);return p.useDebugValue(l),l};return Object.assign(o,{withTypes:()=>o}),o}var Ae=ae();var Et=B;0&&(module.exports={Provider,ReactReduxContext,batch,connect,createDispatchHook,createSelectorHook,createStoreHook,shallowEqual,useDispatch,useSelector,useStore});
//# sourceMappingURL=react-redux.production.min.cjs.map