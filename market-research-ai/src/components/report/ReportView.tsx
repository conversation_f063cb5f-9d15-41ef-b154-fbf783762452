'use client'

import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { useMarketResearchStore } from '@/store'
import ReportBlock from './ReportBlock'
import AIActions from './AIActions'

const ReportView = () => {
  const { 
    currentReport, 
    createReport, 
    updateReport,
    addReportBlock 
  } = useMarketResearchStore()
  
  const [isEditing, setIsEditing] = useState(false)

  const handleCreateReport = () => {
    createReport('Bangkok Cafe & Restaurant Market Analysis')
    setIsEditing(true)
  }

  const handleAddBlock = (type: string) => {
    if (!currentReport) return

    const content = getDefaultContent(type)
    addReportBlock(currentReport.id, { type: type as any, content })
  }

  const getDefaultContent = (type: string) => {
    switch (type) {
      case 'heading':
        return 'New Heading'
      case 'text':
        return 'Start writing...'
      case 'list':
        return ['First item', 'Second item']
      case 'quote':
        return 'Insert quote here...'
      case 'chart':
        return {
          type: 'bar',
          data: {
            labels: ['Q1', 'Q2', 'Q3', 'Q4'],
            datasets: [{
              label: 'Sample Data',
              data: [65, 59, 80, 81]
            }]
          }
        }
      default:
        return ''
    }
  }

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: { 
      opacity: 1,
      transition: {
        duration: 0.6,
        staggerChildren: 0.1
      }
    }
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: { duration: 0.4, ease: "easeOut" }
    }
  }

  if (!currentReport) {
    return (
      <motion.div 
        className="h-full flex items-center justify-center relative overflow-hidden"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.6 }}
      >
        {/* Background gradient */}
        <div className="absolute inset-0 bg-gradient-to-br from-background-start via-background-end to-background-start opacity-70" />
        
        <motion.div 
          className="text-center relative z-10"
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          <motion.div 
            className="w-20 h-20 mx-auto mb-6 rounded-3xl glass border border-white/10 shadow-glass flex items-center justify-center"
            variants={itemVariants}
            whileHover={{ scale: 1.05, rotate: 5 }}
          >
            <span className="material-symbols-outlined text-3xl text-primary-400">description</span>
          </motion.div>
          
          <motion.h3 
            className="text-2xl font-bold text-white mb-3"
            variants={itemVariants}
          >
            No Report Selected
          </motion.h3>
          
          <motion.p 
            className="text-text-tertiary mb-8 max-w-md"
            variants={itemVariants}
          >
            Create a new market research report to get started with AI-powered analysis
          </motion.p>
          
          <motion.button
            onClick={handleCreateReport}
            className="gradient-primary rounded-2xl px-8 py-4 text-sm font-bold text-white shadow-glow hover:shadow-glow-purple transition-all duration-300 flex items-center space-x-3 mx-auto"
            variants={itemVariants}
            whileHover={{ scale: 1.05, y: -2 }}
            whileTap={{ scale: 0.95 }}
          >
            <span className="material-symbols-outlined text-lg">add</span>
            <span>Create New Report</span>
          </motion.button>
        </motion.div>
      </motion.div>
    )
  }

  return (
    <motion.div 
      className="h-full flex flex-col relative overflow-hidden"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.6 }}
    >
      {/* Background gradient */}
      <div className="absolute inset-0 bg-gradient-to-br from-background-start via-background-end to-background-start opacity-50" />
      
      {/* Report header */}
      <motion.div 
        className="glass border-b border-white/10 backdrop-blur-3xl px-8 py-6 relative z-10"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <div className="flex items-center justify-between">
          <div className="flex-1 min-w-0">
            <AnimatePresence mode="wait">
              {isEditing ? (
                <motion.input
                  key="editing"
                  type="text"
                  value={currentReport.title}
                  onChange={(e) => updateReport(currentReport.id, { title: e.target.value })}
                  onBlur={() => setIsEditing(false)}
                  onKeyPress={(e) => e.key === 'Enter' && setIsEditing(false)}
                  className="text-3xl font-bold text-white bg-transparent border-none focus:outline-none focus:ring-0 w-full placeholder-text-muted"
                  autoFocus
                  initial={{ opacity: 0, scale: 0.95 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0.95 }}
                />
              ) : (
                <motion.h1 
                  key="display"
                  className="text-3xl font-bold text-white leading-tight cursor-pointer hover:text-text-secondary transition-colors"
                  onClick={() => setIsEditing(true)}
                  initial={{ opacity: 0, scale: 0.95 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0.95 }}
                  whileHover={{ scale: 1.02 }}
                >
                  {currentReport.title}
                </motion.h1>
              )}
            </AnimatePresence>
            
            <motion.p 
              className="text-sm text-text-tertiary mt-2 flex items-center space-x-2"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.2 }}
            >
              <span className="material-symbols-outlined text-sm">schedule</span>
              <span>Last updated: {currentReport.updatedAt.toLocaleDateString()}</span>
            </motion.p>
          </div>
          
          <motion.div 
            className="flex items-center space-x-3"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.3 }}
          >
            <motion.button 
              className="glass-hover rounded-xl px-4 py-2 text-sm font-medium text-text-secondary hover:text-white transition-all duration-300 flex items-center space-x-2"
              whileHover={{ scale: 1.05, y: -2 }}
              whileTap={{ scale: 0.95 }}
            >
              <span className="material-symbols-outlined text-base">share</span>
              <span>Share</span>
            </motion.button>
            
            <motion.button 
              className="gradient-secondary rounded-xl px-4 py-2 text-sm font-bold text-white shadow-glow hover:shadow-glow-purple transition-all duration-300 flex items-center space-x-2"
              whileHover={{ scale: 1.05, y: -2 }}
              whileTap={{ scale: 0.95 }}
            >
              <span className="material-symbols-outlined text-base">file_download</span>
              <span>Export</span>
            </motion.button>
          </motion.div>
        </div>
      </motion.div>

      {/* Report content */}
      <div className="flex-1 overflow-y-auto relative z-10">
        <motion.div 
          className="max-w-4xl mx-auto p-8"
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          <motion.div className="space-y-8">
            <AnimatePresence>
              {currentReport.content.map((block, index) => (
                <motion.div
                  key={block.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ 
                    duration: 0.4,
                    delay: index * 0.1,
                    ease: "easeOut"
                  }}
                >
                  <ReportBlock 
                    block={block} 
                    reportId={currentReport.id}
                    isLast={index === currentReport.content.length - 1}
                  />
                </motion.div>
              ))}
            </AnimatePresence>

            {/* AI Actions */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ 
                duration: 0.5, 
                delay: currentReport.content.length * 0.1 + 0.2
              }}
            >
              <AIActions reportId={currentReport.id} />
            </motion.div>
          </motion.div>
        </motion.div>
      </div>
    </motion.div>
  )
}

export default ReportView