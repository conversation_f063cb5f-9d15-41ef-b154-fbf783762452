'use client'

import { motion } from 'framer-motion'
import { AgentState } from '@/store'

interface AgentStateDisplayProps {
  state: AgentState
}

const AgentStateDisplay: React.FC<AgentStateDisplayProps> = ({ state }) => {
  const getStateConfig = (status: string) => {
    switch (status) {
      case 'thinking':
        return {
          icon: 'psychology',
          color: 'text-yellow-400',
          bgColor: 'bg-yellow-400/20',
          borderColor: 'border-yellow-400/30',
          text: 'Thinking...',
          description: 'Processing your request'
        }
      case 'analyzing':
        return {
          icon: 'analytics',
          color: 'text-blue-400',
          bgColor: 'bg-blue-400/20',
          borderColor: 'border-blue-400/30',
          text: 'Analyzing',
          description: 'Analyzing market data'
        }
      case 'generating':
        return {
          icon: 'auto_awesome',
          color: 'text-purple-400',
          bgColor: 'bg-purple-400/20',
          borderColor: 'border-purple-400/30',
          text: 'Generating',
          description: 'Creating report content'
        }
      case 'researching':
        return {
          icon: 'search',
          color: 'text-green-400',
          bgColor: 'bg-green-400/20',
          borderColor: 'border-green-400/30',
          text: 'Researching',
          description: 'Gathering market insights'
        }
      default:
        return {
          icon: 'smart_toy',
          color: 'text-slate-400',
          bgColor: 'bg-slate-400/20',
          borderColor: 'border-slate-400/30',
          text: 'Ready',
          description: 'Standing by'
        }
    }
  }

  const config = getStateConfig(state.status)

  const pulseVariants = {
    pulse: {
      scale: [1, 1.1, 1],
      opacity: [0.7, 1, 0.7],
      transition: {
        duration: 2,
        repeat: Infinity,
        ease: "easeInOut"
      }
    }
  }

  const containerVariants = {
    hidden: { opacity: 0, scale: 0.8 },
    visible: { 
      opacity: 1, 
      scale: 1,
      transition: {
        duration: 0.3,
        ease: "easeOut"
      }
    },
    exit: {
      opacity: 0,
      scale: 0.8,
      transition: {
        duration: 0.2
      }
    }
  }

  return (
    <motion.div
      className={`flex items-center gap-3 px-4 py-2 rounded-xl border ${config.bgColor} ${config.borderColor} backdrop-blur-sm`}
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      exit="exit"
    >
      {/* Animated Icon */}
      <motion.div
        className={`${config.color}`}
        variants={pulseVariants}
        animate="pulse"
      >
        <span className="material-symbols-outlined text-lg">{config.icon}</span>
      </motion.div>

      {/* Status Text */}
      <div className="flex flex-col">
        <span className={`text-sm font-medium ${config.color}`}>
          {config.text}
        </span>
        <span className="text-xs text-slate-400">
          {config.description}
        </span>
      </div>

      {/* Progress Indicator */}
      {state.progress !== undefined && (
        <div className="flex items-center gap-2 ml-2">
          <div className="w-16 bg-white/10 rounded-full h-1">
            <motion.div
              className={`h-1 rounded-full ${config.color.replace('text-', 'bg-')}`}
              initial={{ width: 0 }}
              animate={{ width: `${state.progress * 100}%` }}
              transition={{ duration: 0.5 }}
            />
          </div>
          <span className="text-xs text-slate-400">
            {Math.round(state.progress * 100)}%
          </span>
        </div>
      )}

      {/* Animated Dots for Active States */}
      {['thinking', 'analyzing', 'generating', 'researching'].includes(state.status) && (
        <div className="flex items-center gap-1 ml-2">
          {[0, 1, 2].map((index) => (
            <motion.div
              key={index}
              className={`w-1.5 h-1.5 rounded-full ${config.color.replace('text-', 'bg-')}`}
              animate={{
                scale: [1, 1.5, 1],
                opacity: [0.5, 1, 0.5]
              }}
              transition={{
                duration: 1,
                repeat: Infinity,
                delay: index * 0.2,
                ease: "easeInOut"
              }}
            />
          ))}
        </div>
      )}
    </motion.div>
  )
}

export default AgentStateDisplay
