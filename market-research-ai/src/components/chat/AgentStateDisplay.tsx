'use client'

import { motion, AnimatePresence } from 'framer-motion'
import { AgentState } from '@/store'

interface AgentStateDisplayProps {
  state: AgentState
}

const AgentStateDisplay = ({ state }: AgentStateDisplayProps) => {
  const getStatusConfig = (status: AgentState['status']) => {
    switch (status) {
      case 'idle': 
        return { 
          color: 'text-text-muted', 
          bgColor: 'bg-gray-500/20', 
          icon: 'pause_circle',
          gradient: 'from-gray-400 to-gray-600'
        }
      case 'thinking': 
        return { 
          color: 'text-yellow-400', 
          bgColor: 'bg-yellow-500/20', 
          icon: 'psychology',
          gradient: 'from-yellow-400 to-orange-500'
        }
      case 'searching': 
        return { 
          color: 'text-blue-400', 
          bgColor: 'bg-blue-500/20', 
          icon: 'search',
          gradient: 'from-blue-400 to-cyan-500'
        }
      case 'analyzing': 
        return { 
          color: 'text-purple-400', 
          bgColor: 'bg-purple-500/20', 
          icon: 'analytics',
          gradient: 'from-purple-400 to-pink-500'
        }
      case 'generating': 
        return { 
          color: 'text-green-400', 
          bgColor: 'bg-green-500/20', 
          icon: 'auto_awesome',
          gradient: 'from-green-400 to-emerald-500'
        }
      default: 
        return { 
          color: 'text-text-muted', 
          bgColor: 'bg-gray-500/20', 
          icon: 'radio_button_unchecked',
          gradient: 'from-gray-400 to-gray-600'
        }
    }
  }

  const statusConfig = getStatusConfig(state.status)

  return (
    <motion.div 
      className="glass rounded-2xl p-4 border border-white/10 shadow-glass"
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.3 }}
    >
      {/* Status Header */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-3">
          <motion.div 
            className={`w-8 h-8 rounded-full bg-gradient-to-r ${statusConfig.gradient} flex items-center justify-center shadow-glow`}
            animate={{ 
              scale: [1, 1.1, 1],
              rotate: state.status === 'thinking' ? [0, 360] : [0, 0]
            }}
            transition={{ 
              duration: 2, 
              repeat: Infinity,
              ease: "easeInOut"
            }}
          >
            <span className="material-symbols-outlined text-white text-sm">
              {statusConfig.icon}
            </span>
          </motion.div>
          
          <div>
            <motion.span 
              className={`text-sm font-semibold capitalize ${statusConfig.color}`}
              initial={{ opacity: 0, x: -10 }}
              animate={{ opacity: 1, x: 0 }}
              key={state.status}
            >
              {state.status}
            </motion.span>
            {state.currentTool && (
              <motion.div 
                className="flex items-center space-x-1 text-xs text-text-tertiary mt-1"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
              >
                <span className="material-symbols-outlined text-xs">build</span>
                <span>Using: {state.currentTool}</span>
              </motion.div>
            )}
          </div>
        </div>
        
        {state.progress !== undefined && (
          <motion.div 
            className="flex items-center space-x-2"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
          >
            <span className="text-xs text-text-tertiary font-medium">
              {Math.round(state.progress * 100)}%
            </span>
            <div className="w-12 h-1 bg-white/10 rounded-full overflow-hidden">
              <motion.div 
                className={`h-full bg-gradient-to-r ${statusConfig.gradient}`}
                initial={{ width: 0 }}
                animate={{ width: `${state.progress * 100}%` }}
                transition={{ duration: 0.5, ease: "easeOut" }}
              />
            </div>
          </motion.div>
        )}
      </div>

      {/* Tasks */}
      <AnimatePresence>
        {state.todos && state.todos.length > 0 && (
          <motion.div 
            className="mb-4"
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: "auto" }}
            exit={{ opacity: 0, height: 0 }}
          >
            <h4 className="text-xs font-semibold text-text-secondary mb-3 flex items-center space-x-2">
              <span className="material-symbols-outlined text-sm">task_alt</span>
              <span>Current Tasks</span>
            </h4>
            <div className="space-y-2">
              {state.todos.slice(0, 3).map((todo, index) => (
                <motion.div 
                  key={todo.id}
                  className="flex items-center space-x-3 text-xs"
                  initial={{ opacity: 0, x: -10 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <motion.div
                    className={`w-2 h-2 rounded-full ${
                      todo.status === 'completed' ? 'bg-green-400' :
                      todo.status === 'in_progress' ? 'bg-yellow-400' : 'bg-gray-400'
                    }`}
                    animate={{
                      scale: todo.status === 'in_progress' ? [1, 1.3, 1] : [1],
                      opacity: todo.status === 'in_progress' ? [1, 0.6, 1] : [1]
                    }}
                    transition={{ duration: 1.5, repeat: Infinity }}
                  />
                  <span className={`flex-1 ${
                    todo.status === 'completed' ? 'text-text-muted line-through' : 'text-text-secondary'
                  }`}>
                    {todo.content}
                  </span>
                </motion.div>
              ))}
              {state.todos.length > 3 && (
                <div className="text-xs text-text-muted ml-5">
                  +{state.todos.length - 3} more tasks...
                </div>
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Files */}
      <AnimatePresence>
        {state.files && Object.keys(state.files).length > 0 && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: "auto" }}
            exit={{ opacity: 0, height: 0 }}
          >
            <h4 className="text-xs font-semibold text-text-secondary mb-3 flex items-center space-x-2">
              <span className="material-symbols-outlined text-sm">folder</span>
              <span>Research Files</span>
            </h4>
            <div className="flex flex-wrap gap-2">
              {Object.keys(state.files).slice(0, 4).map((filename, index) => (
                <motion.span 
                  key={filename}
                  className="glass px-2 py-1 rounded-full text-xs text-text-secondary border border-white/10"
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: index * 0.1 }}
                  whileHover={{ scale: 1.05 }}
                >
                  {filename.length > 15 ? filename.substring(0, 12) + '...' : filename}
                </motion.span>
              ))}
              {Object.keys(state.files).length > 4 && (
                <span className="glass px-2 py-1 rounded-full text-xs text-text-muted border border-white/10">
                  +{Object.keys(state.files).length - 4}
                </span>
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  )
}

export default AgentStateDisplay