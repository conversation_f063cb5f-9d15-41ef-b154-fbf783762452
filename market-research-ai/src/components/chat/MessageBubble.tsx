'use client'

import { motion } from 'framer-motion'
import { ChatMessage } from '@/store'

interface MessageBubbleProps {
  message: ChatMessage
}

const MessageBubble: React.FC<MessageBubbleProps> = ({ message }) => {
  const isUser = message.type === 'user'
  const isAssistant = message.type === 'assistant'
  const isSystem = message.type === 'system'

  const bubbleVariants = {
    hidden: { opacity: 0, y: 20, scale: 0.95 },
    visible: { 
      opacity: 1, 
      y: 0, 
      scale: 1,
      transition: {
        duration: 0.4,
        ease: "easeOut"
      }
    }
  }

  const formatTime = (timestamp: Date) => {
    return timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
  }

  if (isSystem) {
    return (
      <motion.div
        className="flex justify-center my-4"
        variants={bubbleVariants}
        initial="hidden"
        animate="visible"
      >
        <div className="px-4 py-2 bg-white/5 border border-white/10 rounded-full text-xs text-slate-400">
          {message.content}
        </div>
      </motion.div>
    )
  }

  return (
    <motion.div
      className={`flex ${isUser ? 'justify-end' : 'justify-start'} mb-6`}
      variants={bubbleVariants}
      initial="hidden"
      animate="visible"
    >
      <div className={`flex items-start gap-3 max-w-[80%] ${isUser ? 'flex-row-reverse' : 'flex-row'}`}>
        {/* Avatar */}
        <motion.div 
          className={`w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 ${
            isUser 
              ? 'bg-gradient-primary shadow-glow' 
              : 'bg-slate-700 border border-white/20'
          }`}
          whileHover={{ scale: 1.1 }}
          transition={{ duration: 0.2 }}
        >
          <span className="material-symbols-outlined text-white text-sm">
            {isUser ? 'person' : 'smart_toy'}
          </span>
        </motion.div>

        {/* Message Content */}
        <div className={`flex flex-col ${isUser ? 'items-end' : 'items-start'}`}>
          {/* Message Bubble */}
          <motion.div
            className={`px-4 py-3 rounded-2xl max-w-full ${
              isUser
                ? 'bg-gradient-primary text-white shadow-glow'
                : 'glass-panel border border-white/10 text-slate-100'
            }`}
            whileHover={{ scale: 1.02 }}
            transition={{ duration: 0.2 }}
          >
            {/* Message Text */}
            <div className="text-sm leading-relaxed whitespace-pre-wrap">
              {message.content}
            </div>

            {/* Code blocks or special content */}
            {message.metadata?.codeBlocks && (
              <div className="mt-3 space-y-2">
                {message.metadata.codeBlocks.map((block, index) => (
                  <div key={index} className="bg-black/20 rounded-lg p-3 border border-white/10">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-xs text-slate-400">{block.language}</span>
                      <button className="text-xs text-slate-400 hover:text-white transition-colors">
                        Copy
                      </button>
                    </div>
                    <pre className="text-xs text-slate-200 overflow-x-auto">
                      <code>{block.code}</code>
                    </pre>
                  </div>
                ))}
              </div>
            )}

            {/* Attachments */}
            {message.attachments && message.attachments.length > 0 && (
              <div className="mt-3 space-y-2">
                {message.attachments.map((attachment, index) => (
                  <div key={index} className="flex items-center gap-2 p-2 bg-white/10 rounded-lg">
                    <span className="material-symbols-outlined text-sm">attach_file</span>
                    <span className="text-xs">{attachment.name}</span>
                    <span className="text-xs text-slate-400">({attachment.size})</span>
                  </div>
                ))}
              </div>
            )}
          </motion.div>

          {/* Message Footer */}
          <div className={`flex items-center gap-2 mt-1 text-xs text-slate-500 ${
            isUser ? 'flex-row-reverse' : 'flex-row'
          }`}>
            <span>{formatTime(message.timestamp)}</span>
            
            {/* Message Status for user messages */}
            {isUser && (
              <div className="flex items-center gap-1">
                {message.status === 'sending' && (
                  <div className="w-3 h-3 border border-slate-400 border-t-transparent rounded-full animate-spin" />
                )}
                {message.status === 'sent' && (
                  <span className="material-symbols-outlined text-xs text-slate-400">check</span>
                )}
                {message.status === 'delivered' && (
                  <span className="material-symbols-outlined text-xs text-green-400">done_all</span>
                )}
                {message.status === 'error' && (
                  <span className="material-symbols-outlined text-xs text-red-400">error</span>
                )}
              </div>
            )}

            {/* Agent State for assistant messages */}
            {isAssistant && message.agentState && (
              <div className="flex items-center gap-1">
                <div className={`w-2 h-2 rounded-full ${
                  message.agentState.status === 'thinking' ? 'bg-yellow-400 animate-pulse' :
                  message.agentState.status === 'generating' ? 'bg-blue-400 animate-pulse' :
                  message.agentState.status === 'analyzing' ? 'bg-purple-400 animate-pulse' :
                  'bg-green-400'
                }`} />
                <span className="capitalize">{message.agentState.status}</span>
              </div>
            )}
          </div>

          {/* Confidence Score for AI messages */}
          {isAssistant && message.confidence && (
            <div className="mt-2 flex items-center gap-2">
              <span className="text-xs text-slate-500">Confidence:</span>
              <div className="w-16 bg-white/10 rounded-full h-1">
                <motion.div
                  className="h-1 bg-gradient-primary rounded-full"
                  initial={{ width: 0 }}
                  animate={{ width: `${message.confidence * 100}%` }}
                  transition={{ duration: 1, delay: 0.5 }}
                />
              </div>
              <span className="text-xs text-slate-400">{Math.round(message.confidence * 100)}%</span>
            </div>
          )}

          {/* Quick Actions */}
          {isAssistant && (
            <div className="flex items-center gap-1 mt-2 opacity-0 group-hover:opacity-100 transition-opacity">
              <button className="p-1 rounded hover:bg-white/10 transition-colors" title="Copy message">
                <span className="material-symbols-outlined text-xs text-slate-400">content_copy</span>
              </button>
              <button className="p-1 rounded hover:bg-white/10 transition-colors" title="Regenerate response">
                <span className="material-symbols-outlined text-xs text-slate-400">refresh</span>
              </button>
              <button className="p-1 rounded hover:bg-white/10 transition-colors" title="Add to report">
                <span className="material-symbols-outlined text-xs text-slate-400">note_add</span>
              </button>
            </div>
          )}
        </div>
      </div>
    </motion.div>
  )
}

export default MessageBubble
