'use client'

import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { useMarketResearchStore } from '@/store'
import { getMarketResearchAgent } from '@/agents/MarketResearchAgent'
import AgentStateDisplay from './AgentStateDisplay'
import MessageBubble from './MessageBubble'
import MapCanvas from '../map/MapCanvas'

const ChatInterface = () => {
  const { messages, addMessage, agentState, isAgentActive, updateAgentState } = useMarketResearchStore()
  const [inputValue, setInputValue] = useState('')
  const [isMapCollapsed, setIsMapCollapsed] = useState(false)

  const handleSendMessage = async () => {
    if (!inputValue.trim()) return
    
    // Add user message
    addMessage({
      type: 'user',
      content: inputValue,
    })
    
    // Update agent state to active
    updateAgentState({ status: 'thinking' })
    
    // Process with agent
    const agent = getMarketResearchAgent()
    const userMessage = inputValue
    setInputValue('')
    
    await agent.processMessage(userMessage)
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  const isProcessing = agentState.status !== 'idle'

  const quickActions = [
    {
      label: "Market trends analysis",
      query: "Analyze the current cafe market trends in Bangkok's Sukhumvit area",
      icon: "trending_up"
    },
    {
      label: "Competitor analysis",
      query: "Find competitor analysis for coffee shops in Thonglor",
      icon: "analytics"
    },
    {
      label: "Location insights",
      query: "Generate location recommendations for opening a new cafe in Bangkok",
      icon: "location_on"
    }
  ]

  return (
    <div className="flex h-full relative overflow-hidden">
      {/* Background gradient */}
      <div className="absolute inset-0 bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900" />
      
      {/* Chat Panel */}
      <motion.div 
        className={`glass-panel relative z-10 border-r border-white/10 backdrop-blur-xl transition-all duration-500 ${
          isMapCollapsed ? 'w-full' : 'w-[400px]'
        }`}
        initial={{ opacity: 0, x: -20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.3 }}
      >
        {/* Chat Header */}
        <div className="relative p-6 border-b border-white/10">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-2xl font-bold text-white">
                AI Chat
              </h2>
              <p className="text-sm text-slate-400 mt-1">
                Market research assistant for Bangkok's F&B industry
              </p>
            </div>
            
            <button
              onClick={() => setIsMapCollapsed(!isMapCollapsed)}
              className="btn-secondary p-2 rounded-xl"
            >
              <span className="material-symbols-outlined">
                {isMapCollapsed ? 'expand_more' : 'chevron_left'}
              </span>
            </button>
          </div>
          
          {/* Agent State Display */}
          {isProcessing && (
            <div className="mt-4">
              <AgentStateDisplay state={agentState} />
            </div>
          )}
        </div>

        {/* Messages */}
        <div className="flex-1 overflow-y-auto p-6 space-y-6">
          {messages.map((message, index) => (
            <MessageBubble key={index} message={message} />
          ))}
        </div>

        {/* Input Area */}
        <div className="border-t border-white/10 p-6">
          <div className="relative mb-4">
            <div className="flex gap-3">
              <textarea
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder="Ask about Bangkok's cafe and restaurant market..."
                className="input-primary resize-none min-h-[50px] max-h-[120px]"
                rows={2}
              />
              <button
                onClick={handleSendMessage}
                disabled={!inputValue.trim() || isProcessing}
                className="btn-primary px-6 py-3 self-end"
              >
                Send
              </button>
            </div>
          </div>
          
          {/* Quick Actions */}
          <div className="flex flex-wrap gap-2">
            {quickActions.map((action, index) => (
              <button
                key={index}
                onClick={() => setInputValue(action.query)}
                className="btn-secondary text-xs px-3 py-2 rounded-lg flex items-center gap-2"
              >
                <span className="material-symbols-outlined text-sm">
                  {action.icon}
                </span>
                {action.label}
              </button>
            ))}
          </div>
        </div>
      </motion.div>

      {/* Map Section */}
      {!isMapCollapsed && (
        <motion.div 
          className="flex-1 relative"
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.3, delay: 0.1 }}
        >
          <div className="absolute inset-0">
            <div className="h-full flex flex-col">
              {/* Map Header */}
              <div className="glass-panel p-6 border-b border-white/10 m-4 mb-0 rounded-t-xl">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="text-xl font-bold text-white">Bangkok Market Map</h3>
                    <p className="text-sm text-slate-400">Interactive data visualization for F&B market research</p>
                  </div>
                  <div className="flex items-center gap-3">
                    <span className="text-sm text-slate-400">Layers:</span>
                    <button className="btn-secondary text-xs px-3 py-1">Restaurants</button>
                    <button className="btn-secondary text-xs px-3 py-1">Demographics</button>
                  </div>
                </div>
              </div>
              
              {/* Map Container */}
              <div className="flex-1 relative mx-4 mb-4">
                <div className="glass-panel h-full rounded-b-xl overflow-hidden">
                  <MapCanvas />
                </div>
                
                {/* Map Legend */}
                <div className="absolute bottom-4 left-4 glass-panel p-4 rounded-lg">
                  <h4 className="text-sm font-semibold text-white mb-2">Legend</h4>
                  <div className="space-y-2 text-xs">
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                      <span className="text-slate-300">Cafes</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 bg-orange-500 rounded-full"></div>
                      <span className="text-slate-300">Restaurants</span>
                    </div>
                    <div className="text-slate-400">Heat intensity = Restaurant density</div>
                  </div>
                </div>
              </div>
              
              {/* Map Controls */}
              <div className="absolute top-20 right-6 flex flex-col gap-2">
                <button className="btn-secondary p-2 rounded-lg">
                  <span className="material-symbols-outlined">zoom_in</span>
                </button>
                <button className="btn-secondary p-2 rounded-lg">
                  <span className="material-symbols-outlined">zoom_out</span>
                </button>
                <button className="btn-secondary p-2 rounded-lg">
                  <span className="material-symbols-outlined">layers</span>
                </button>
                <button className="btn-secondary p-2 rounded-lg">
                  <span className="material-symbols-outlined">my_location</span>
                </button>
              </div>
            </div>
          </div>
        </motion.div>
      )}
    </div>
  )
}

export default ChatInterface