'use client'

import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { useMarketResearchStore, useLayout, useNotifications, useActiveReport } from '@/store'
import SearchBar from '@/components/shared/SearchBar'

const Header = () => {
  const layout = useLayout()
  const notifications = useNotifications()
  const activeReport = useActiveReport()
  const [showSearch, setShowSearch] = useState(false)
  const [showNotifications, setShowNotifications] = useState(false)
  
  const { 
    toggleLeftSidebar, 
    toggleRightSidebar, 
    setActivePanel,
    addNotification,
    markNotificationRead 
  } = useMarketResearchStore()

  const containerVariants = {
    hidden: { opacity: 0, y: -20 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: {
        duration: 0.6,
        ease: "easeOut",
        staggerChildren: 0.1
      }
    }
  }

  const itemVariants = {
    hidden: { opacity: 0, y: -10 },
    visible: { opacity: 1, y: 0 }
  }

  const getContextualTitle = () => {
    switch (layout.activePanel) {
      case 'chat':
        return {
          title: 'AI Chat & Research',
          subtitle: "AI-powered market research for Bangkok's F&B industry"
        }
      case 'reports':
        return {
          title: activeReport?.title || 'Reports & Analytics',
          subtitle: 'AI-enhanced market research reporting'
        }
      case 'map':
        return {
          title: 'Bangkok Market Map',
          subtitle: 'Interactive data visualization and insights'
        }
      default:
        return {
          title: 'MarketMind Dashboard',
          subtitle: 'Comprehensive market research platform'
        }
    }
  }

  const contextualData = getContextualTitle()

  return (
    <motion.header 
      className="glass sticky top-0 z-50 border-b border-white/10 backdrop-blur-3xl"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      <div className="flex items-center justify-between px-6 py-4">
        {/* Logo and Title Section */}
        <motion.div 
          className="flex items-center space-x-6"
          variants={itemVariants}
        >
          {/* Animated Logo */}
          <motion.div 
            className="flex items-center space-x-3"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <div className="relative">
              <motion.div 
                className="w-10 h-10 gradient-primary rounded-2xl flex items-center justify-center shadow-glow"
                animate={{ 
                  boxShadow: [
                    "0 0 20px rgba(99, 102, 241, 0.3)",
                    "0 0 30px rgba(139, 92, 246, 0.5)",
                    "0 0 20px rgba(99, 102, 241, 0.3)"
                  ]
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  ease: "easeInOut"
                }}
              >
                <span className="material-symbols-outlined text-white text-lg">analytics</span>
              </motion.div>
            </div>
            
            <div>
              <h1 className="text-xl font-bold text-white">MarketMind</h1>
              <p className="text-xs text-slate-400">AI Research Platform</p>
            </div>
          </motion.div>

          {/* Panel Toggle Buttons */}
          <motion.div 
            className="flex items-center gap-2"
            variants={itemVariants}
          >
            <button
              onClick={toggleLeftSidebar}
              className={`btn-secondary p-2 rounded-lg transition-colors ${
                !layout.leftSidebarCollapsed ? 'bg-primary-500/20 text-primary-400' : ''
              }`}
              title="Toggle chat panel"
            >
              <span className="material-symbols-outlined">chat</span>
            </button>
            
            <button
              onClick={toggleRightSidebar}
              className={`btn-secondary p-2 rounded-lg transition-colors ${
                !layout.rightSidebarCollapsed ? 'bg-primary-500/20 text-primary-400' : ''
              }`}
              title="Toggle reports panel"
            >
              <span className="material-symbols-outlined">description</span>
            </button>
          </motion.div>

          {/* Dynamic Title */}
          <motion.div 
            className="hidden md:block border-l border-white/20 pl-6"
            variants={itemVariants}
          >
            <h2 className="text-lg font-semibold text-white">
              {contextualData.title}
            </h2>
            <p className="text-sm text-slate-400">
              {contextualData.subtitle}
            </p>
          </motion.div>
        </motion.div>

        {/* Center - Search Bar */}
        <motion.div 
          className="flex-1 max-w-md mx-8 hidden lg:block"
          variants={itemVariants}
        >
          <SearchBar 
            placeholder="Search conversations, reports, locations..."
            onSearch={(query) => console.log('Search:', query)}
          />
        </motion.div>

        {/* Actions */}
        <motion.div 
          className="flex items-center space-x-3"
          variants={itemVariants}
        >
          {/* Mobile Search Toggle */}
          <motion.button 
            className="btn-secondary p-3 rounded-xl hover:bg-white/10 transition-colors lg:hidden"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={() => setShowSearch(!showSearch)}
          >
            <span className="material-symbols-outlined">search</span>
          </motion.button>

          {/* Notifications */}
          <motion.div className="relative">
            <motion.button 
              className="btn-secondary p-3 rounded-xl hover:bg-white/10 transition-colors relative"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => setShowNotifications(!showNotifications)}
            >
              <span className="material-symbols-outlined">notifications</span>
              {notifications.length > 0 && (
                <div className="absolute -top-1 -right-1 w-5 h-5 bg-red-500 rounded-full flex items-center justify-center">
                  <span className="text-xs text-white font-bold">
                    {notifications.length > 9 ? '9+' : notifications.length}
                  </span>
                </div>
              )}
            </motion.button>

            {/* Notifications Dropdown */}
            <AnimatePresence>
              {showNotifications && (
                <motion.div
                  className="absolute right-0 top-full mt-2 w-80 glass-panel border border-white/10 rounded-xl shadow-xl z-50"
                  initial={{ opacity: 0, y: -10, scale: 0.95 }}
                  animate={{ opacity: 1, y: 0, scale: 1 }}
                  exit={{ opacity: 0, y: -10, scale: 0.95 }}
                  transition={{ duration: 0.2 }}
                >
                  <div className="p-4 border-b border-white/10">
                    <h3 className="text-lg font-semibold text-white">Notifications</h3>
                  </div>
                  
                  <div className="max-h-80 overflow-y-auto">
                    {notifications.length === 0 ? (
                      <div className="p-6 text-center text-slate-400">
                        <span className="material-symbols-outlined text-3xl mb-2 block">notifications_off</span>
                        No new notifications
                      </div>
                    ) : (
                      notifications.map((notification) => (
                        <div
                          key={notification.id}
                          className="p-4 border-b border-white/5 hover:bg-white/5 transition-colors cursor-pointer"
                          onClick={() => markNotificationRead(notification.id)}
                        >
                          <div className="flex items-start gap-3">
                            <div className={`w-2 h-2 rounded-full mt-2 ${
                              notification.type === 'error' ? 'bg-red-500' :
                              notification.type === 'warning' ? 'bg-yellow-500' :
                              notification.type === 'success' ? 'bg-green-500' :
                              'bg-blue-500'
                            }`} />
                            <div className="flex-1">
                              <h4 className="text-sm font-medium text-white">{notification.title}</h4>
                              <p className="text-xs text-slate-400 mt-1">{notification.message}</p>
                              <p className="text-xs text-slate-500 mt-2">
                                {notification.timestamp.toLocaleTimeString()}
                              </p>
                            </div>
                          </div>
                        </div>
                      ))
                    )}
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </motion.div>

          {/* Settings */}
          <motion.button 
            className="btn-secondary p-3 rounded-xl hover:bg-white/10 transition-colors hidden md:flex"
            whileHover={{ scale: 1.05, rotate: 90 }}
            whileTap={{ scale: 0.95 }}
            transition={{ duration: 0.2 }}
          >
            <span className="material-symbols-outlined">settings</span>
          </motion.button>

          {/* Export */}
          <motion.button 
            className="gradient-primary rounded-xl px-4 py-2 text-sm font-bold text-white shadow-glow hover:shadow-glow-purple transition-all duration-300 flex items-center space-x-2"
            whileHover={{ scale: 1.05, y: -2 }}
            whileTap={{ scale: 0.95 }}
          >
            <span className="material-symbols-outlined text-base">file_download</span>
            <span className="hidden sm:inline">Export</span>
          </motion.button>

          {/* User Avatar */}
          <motion.div 
            className="w-10 h-10 rounded-full bg-gradient-primary shadow-glow flex items-center justify-center cursor-pointer"
            whileHover={{ scale: 1.1, rotate: 5 }}
            whileTap={{ scale: 0.9 }}
          >
            <span className="material-symbols-outlined text-white text-lg">person</span>
          </motion.div>
        </motion.div>
      </div>

      {/* Mobile Search Bar */}
      <AnimatePresence>
        {showSearch && (
          <motion.div
            className="px-6 pb-4 lg:hidden"
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3 }}
          >
            <SearchBar 
              placeholder="Search conversations, reports, locations..."
              onSearch={(query) => console.log('Search:', query)}
            />
          </motion.div>
        )}
      </AnimatePresence>
    </motion.header>
  )
}

export default Header
