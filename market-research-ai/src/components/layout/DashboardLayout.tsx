'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { useMarketResearchStore, useLayout } from '@/store'
import Header from './Header'
import Sidebar from './Sidebar'
import Panel from './Panel'
import ChatInterface from '@/components/chat/ChatInterface'
import MapCanvas from '@/components/map/MapCanvas'
import ReportDashboard from '@/components/reports/ReportDashboard'
import NotificationCenter from '@/components/shared/NotificationCenter'
import { useResponsive } from '@/hooks/useResponsive'

interface DashboardLayoutProps {
  children?: React.ReactNode
}

const DashboardLayout: React.FC<DashboardLayoutProps> = ({ children }) => {
  const layout = useLayout()
  const { isMobile, isTablet, isDesktop } = useResponsive()
  const [draggedPanel, setDraggedPanel] = useState<string | null>(null)

  // Handle responsive layout changes
  useEffect(() => {
    const { toggleLeftSidebar, toggleRightSidebar, toggleBottomPanel } = useMarketResearchStore.getState()
    
    if (isMobile) {
      // On mobile, collapse all panels by default
      if (!layout.leftSidebarCollapsed) toggleLeftSidebar()
      if (!layout.rightSidebarCollapsed) toggleRightSidebar()
      if (!layout.bottomPanelCollapsed) toggleBottomPanel()
    }
  }, [isMobile, layout])

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: { 
      opacity: 1,
      transition: {
        duration: 0.6,
        staggerChildren: 0.1
      }
    }
  }

  const panelVariants = {
    hidden: { opacity: 0, scale: 0.95 },
    visible: { 
      opacity: 1, 
      scale: 1,
      transition: { duration: 0.4, ease: "easeOut" }
    },
    exit: { 
      opacity: 0, 
      scale: 0.95,
      transition: { duration: 0.3 }
    }
  }

  // Calculate grid template based on panel states
  const getGridTemplate = () => {
    if (isMobile) {
      return {
        gridTemplateColumns: '1fr',
        gridTemplateRows: 'auto 1fr auto',
        gridTemplateAreas: `
          "header"
          "main"
          "bottom"
        `
      }
    }

    if (isTablet) {
      const leftCol = layout.leftSidebarCollapsed ? '0' : `${layout.panelSizes.leftSidebar}px`
      const rightCol = layout.rightSidebarCollapsed ? '0' : `${layout.panelSizes.rightSidebar}px`
      
      return {
        gridTemplateColumns: `${leftCol} 1fr ${rightCol}`,
        gridTemplateRows: 'auto 1fr auto',
        gridTemplateAreas: `
          "header header header"
          "left main right"
          "bottom bottom bottom"
        `
      }
    }

    // Desktop layout
    const leftCol = layout.leftSidebarCollapsed ? '0' : `${layout.panelSizes.leftSidebar}px`
    const rightCol = layout.rightSidebarCollapsed ? '0' : `${layout.panelSizes.rightSidebar}px`
    const bottomRow = layout.bottomPanelCollapsed ? '0' : `${layout.panelSizes.bottomPanel}px`
    
    return {
      gridTemplateColumns: `${leftCol} 1fr ${rightCol}`,
      gridTemplateRows: `auto 1fr ${bottomRow}`,
      gridTemplateAreas: `
        "header header header"
        "left main right"
        "bottom bottom bottom"
      `
    }
  }

  return (
    <motion.div 
      className="h-screen w-screen overflow-hidden bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      {/* Animated background */}
      <div className="fixed inset-0 bg-gradient-to-br from-background-start via-background-end to-background-start">
        <motion.div 
          className="absolute inset-0 opacity-30"
          animate={{
            background: [
              "radial-gradient(circle at 20% 50%, rgba(99, 102, 241, 0.3) 0%, transparent 50%)",
              "radial-gradient(circle at 80% 50%, rgba(139, 92, 246, 0.3) 0%, transparent 50%)",
              "radial-gradient(circle at 50% 20%, rgba(99, 102, 241, 0.3) 0%, transparent 50%)",
            ]
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
      </div>

      {/* Main grid layout */}
      <div 
        className="relative z-10 h-full grid transition-all duration-300 ease-smooth"
        style={getGridTemplate()}
      >
        {/* Header */}
        <motion.div 
          className="grid-area-[header] z-50"
          variants={panelVariants}
        >
          <Header />
        </motion.div>

        {/* Left Sidebar - Chat */}
        <AnimatePresence>
          {!layout.leftSidebarCollapsed && (
            <motion.div
              className="grid-area-[left] border-r border-white/10"
              variants={panelVariants}
              initial="hidden"
              animate="visible"
              exit="exit"
              key="left-sidebar"
            >
              <Sidebar
                side="left"
                title="AI Chat"
                subtitle="Market research assistant"
                onResize={(size) => useMarketResearchStore.getState().setPanelSize('leftSidebar', size)}
              >
                <ChatInterface />
              </Sidebar>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Main Content - Map */}
        <motion.div 
          className="grid-area-[main] relative overflow-hidden"
          variants={panelVariants}
        >
          <Panel
            title="Bangkok Market Map"
            subtitle="Interactive data visualization"
            className="h-full"
          >
            <MapCanvas />
          </Panel>
        </motion.div>

        {/* Right Sidebar - Reports */}
        <AnimatePresence>
          {!layout.rightSidebarCollapsed && (
            <motion.div
              className="grid-area-[right] border-l border-white/10"
              variants={panelVariants}
              initial="hidden"
              animate="visible"
              exit="exit"
              key="right-sidebar"
            >
              <Sidebar
                side="right"
                title="Reports & Analytics"
                subtitle="AI-powered insights"
                onResize={(size) => useMarketResearchStore.getState().setPanelSize('rightSidebar', size)}
              >
                <ReportDashboard />
              </Sidebar>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Bottom Panel - Quick Actions & Status */}
        <AnimatePresence>
          {!layout.bottomPanelCollapsed && (
            <motion.div
              className="grid-area-[bottom] border-t border-white/10"
              variants={panelVariants}
              initial="hidden"
              animate="visible"
              exit="exit"
              key="bottom-panel"
            >
              <Panel
                title="Quick Actions"
                subtitle="Shortcuts and status"
                className="h-full"
                collapsible
                onToggle={() => useMarketResearchStore.getState().toggleBottomPanel()}
              >
                <div className="p-4 flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <button className="btn-primary text-sm px-4 py-2">
                      <span className="material-symbols-outlined text-base mr-2">add</span>
                      New Analysis
                    </button>
                    <button className="btn-secondary text-sm px-4 py-2">
                      <span className="material-symbols-outlined text-base mr-2">upload</span>
                      Import Data
                    </button>
                  </div>
                  
                  <div className="flex items-center gap-4 text-sm text-slate-400">
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                      <span>Real-time sync active</span>
                    </div>
                    <div>Last updated: 2 minutes ago</div>
                  </div>
                </div>
              </Panel>
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* Notification Center */}
      <NotificationCenter />

      {/* Mobile Navigation */}
      {isMobile && (
        <motion.div 
          className="fixed bottom-0 left-0 right-0 z-50 glass-panel border-t border-white/10 p-4"
          initial={{ y: 100 }}
          animate={{ y: 0 }}
          transition={{ duration: 0.3 }}
        >
          <div className="flex justify-around">
            <button 
              className={`flex flex-col items-center gap-1 p-2 rounded-lg transition-colors ${
                layout.activePanel === 'chat' ? 'bg-primary-500/20 text-primary-400' : 'text-slate-400'
              }`}
              onClick={() => useMarketResearchStore.getState().setActivePanel('chat')}
            >
              <span className="material-symbols-outlined">chat</span>
              <span className="text-xs">Chat</span>
            </button>
            
            <button 
              className={`flex flex-col items-center gap-1 p-2 rounded-lg transition-colors ${
                layout.activePanel === 'map' ? 'bg-primary-500/20 text-primary-400' : 'text-slate-400'
              }`}
              onClick={() => useMarketResearchStore.getState().setActivePanel('map')}
            >
              <span className="material-symbols-outlined">map</span>
              <span className="text-xs">Map</span>
            </button>
            
            <button 
              className={`flex flex-col items-center gap-1 p-2 rounded-lg transition-colors ${
                layout.activePanel === 'reports' ? 'bg-primary-500/20 text-primary-400' : 'text-slate-400'
              }`}
              onClick={() => useMarketResearchStore.getState().setActivePanel('reports')}
            >
              <span className="material-symbols-outlined">description</span>
              <span className="text-xs">Reports</span>
            </button>
          </div>
        </motion.div>
      )}

      {children}
    </motion.div>
  )
}

export default DashboardLayout
