import { useState, useEffect } from 'react'

interface ResponsiveState {
  isMobile: boolean
  isTablet: boolean
  isDesktop: boolean
  isLarge: boolean
  width: number
  height: number
}

export const useResponsive = (): ResponsiveState => {
  const [state, setState] = useState<ResponsiveState>({
    isMobile: false,
    isTablet: false,
    isDesktop: false,
    isLarge: false,
    width: 0,
    height: 0
  })

  useEffect(() => {
    const updateState = () => {
      const width = window.innerWidth
      const height = window.innerHeight

      setState({
        isMobile: width < 768,
        isTablet: width >= 768 && width < 1024,
        isDesktop: width >= 1024 && width < 1440,
        isLarge: width >= 1440,
        width,
        height
      })
    }

    // Initial update
    updateState()

    // Listen for resize events
    window.addEventListener('resize', updateState)
    
    // Listen for orientation changes on mobile
    window.addEventListener('orientationchange', () => {
      // Delay to ensure dimensions are updated after orientation change
      setTimeout(updateState, 100)
    })

    return () => {
      window.removeEventListener('resize', updateState)
      window.removeEventListener('orientationchange', updateState)
    }
  }, [])

  return state
}

// Breakpoint constants for consistency
export const BREAKPOINTS = {
  mobile: 768,
  tablet: 1024,
  desktop: 1440
} as const

// Utility function to check if current width matches a breakpoint
export const useBreakpoint = (breakpoint: keyof typeof BREAKPOINTS): boolean => {
  const { width } = useResponsive()
  
  switch (breakpoint) {
    case 'mobile':
      return width < BREAKPOINTS.mobile
    case 'tablet':
      return width >= BREAKPOINTS.mobile && width < BREAKPOINTS.tablet
    case 'desktop':
      return width >= BREAKPOINTS.tablet && width < BREAKPOINTS.desktop
    default:
      return false
  }
}

// Hook for media queries
export const useMediaQuery = (query: string): boolean => {
  const [matches, setMatches] = useState(false)

  useEffect(() => {
    const media = window.matchMedia(query)
    
    const updateMatch = () => setMatches(media.matches)
    
    // Initial check
    updateMatch()
    
    // Listen for changes
    media.addEventListener('change', updateMatch)
    
    return () => media.removeEventListener('change', updateMatch)
  }, [query])

  return matches
}
