import { create } from 'zustand'
import { subscribeWithSelector, persist, devtools } from 'zustand/middleware'
import { immer } from 'zustand/middleware/immer'

// Enhanced type definitions for the new architecture
export interface User {
  id: string
  name: string
  avatar?: string
  status: 'online' | 'offline' | 'away'
}

export interface Conversation {
  id: string
  title: string
  messages: ChatMessage[]
  createdAt: Date
  updatedAt: Date
  participants: User[]
  tags: string[]
  archived: boolean
}

export interface ChatMessage {
  id: string
  conversationId: string
  type: 'user' | 'assistant' | 'system'
  content: string
  timestamp: Date
  agentState?: AgentState
  tools?: string[]
  attachments?: MessageAttachment[]
  reactions?: MessageReaction[]
  metadata?: Record<string, any>
}

export interface MessageAttachment {
  id: string
  type: 'image' | 'file' | 'chart' | 'map-location' | 'report-snippet'
  url?: string
  data?: any
  metadata?: Record<string, any>
}

export interface MessageReaction {
  emoji: string
  userId: string
  timestamp: Date
}

export interface AgentState {
  status: 'idle' | 'thinking' | 'searching' | 'analyzing' | 'generating' | 'error'
  currentTool?: string
  progress?: number
  todos?: Array<{
    id: string
    content: string
    status: 'pending' | 'in_progress' | 'completed'
  }>
  files?: Record<string, string>
  error?: string
}

export interface ChatFilters {
  dateRange?: [Date, Date]
  messageTypes?: ('user' | 'assistant' | 'system')[]
  tags?: string[]
  searchQuery?: string
}

export interface MapViewport {
  center: [number, number] // [lng, lat]
  zoom: number
  bearing: number
  pitch: number
}

export interface MapLayer {
  id: string
  name: string
  type: 'heatmap' | 'markers' | 'choropleth' | 'routes'
  visible: boolean
  opacity: number
  data: any[]
  style?: Record<string, any>
}

export interface MapMarker {
  id: string
  coordinates: [number, number]
  type: 'restaurant' | 'cafe' | 'competitor' | 'opportunity' | 'custom'
  data: any
  clustered?: boolean
  selected?: boolean
}

export interface MapFilters {
  categories?: string[]
  dateRange?: [Date, Date]
  priceRange?: [number, number]
  ratings?: [number, number]
  customFilters?: Record<string, any>
}

export interface Report {
  id: string
  title: string
  description?: string
  content: ReportBlock[]
  createdAt: Date
  updatedAt: Date
  shared: boolean
  collaborators: User[]
  version: number
  tags: string[]
  template?: string
  exportFormats: ('pdf' | 'csv' | 'json' | 'docx')[]
}

export interface ReportBlock {
  id: string
  type: 'text' | 'chart' | 'image' | 'heading' | 'list' | 'quote' | 'ai-suggestion' | 'map-embed' | 'data-table' | 'timeline'
  content: any
  metadata?: any
  position: { x: number; y: number; width: number; height: number }
  locked?: boolean
  comments?: Comment[]
}

export interface Comment {
  id: string
  userId: string
  content: string
  timestamp: Date
  resolved: boolean
  replies?: Comment[]
}

export interface ExportJob {
  id: string
  reportId: string
  format: 'pdf' | 'csv' | 'json' | 'docx'
  status: 'pending' | 'processing' | 'completed' | 'failed'
  progress: number
  downloadUrl?: string
  error?: string
}

// Layout and UI state interfaces
export interface LayoutState {
  leftSidebarCollapsed: boolean
  rightSidebarCollapsed: boolean
  bottomPanelCollapsed: boolean
  activePanel: 'chat' | 'map' | 'reports'
  splitView: boolean
  panelSizes: {
    leftSidebar: number
    rightSidebar: number
    bottomPanel: number
  }
}

export interface UIState {
  theme: 'dark' | 'light' | 'auto'
  notifications: Notification[]
  modals: Modal[]
  loading: Record<string, boolean>
  errors: Record<string, string>
}

export interface Notification {
  id: string
  type: 'info' | 'success' | 'warning' | 'error'
  title: string
  message: string
  timestamp: Date
  read: boolean
  actions?: NotificationAction[]
}

export interface NotificationAction {
  label: string
  action: () => void
  style?: 'primary' | 'secondary' | 'danger'
}

export interface Modal {
  id: string
  type: string
  props: Record<string, any>
  closable: boolean
}

export interface RealTimeState {
  connected: boolean
  lastSync: Date
  pendingUpdates: Update[]
  conflicts: Conflict[]
  typingUsers: Record<string, User[]>
}

export interface Update {
  id: string
  type: 'chat' | 'map' | 'report'
  action: 'create' | 'update' | 'delete'
  data: any
  timestamp: Date
  userId: string
}

export interface Conflict {
  id: string
  type: 'chat' | 'map' | 'report'
  resourceId: string
  conflictingUpdates: Update[]
  resolution?: 'local' | 'remote' | 'merge'
}

// Enhanced main application state
interface MarketResearchState {
  // Layout and UI
  layout: LayoutState
  ui: UIState

  // Chat state (enhanced)
  chat: {
    conversations: Conversation[]
    activeConversationId: string | null
    searchQuery: string
    filters: ChatFilters
    isAgentActive: boolean
    agentState: AgentState
  }

  // Map state (enhanced)
  map: {
    viewport: MapViewport
    layers: MapLayer[]
    markers: MapMarker[]
    selectedFeatures: string[]
    filters: MapFilters
    realTimeUpdates: boolean
    interactionMode: 'view' | 'select' | 'draw' | 'measure'
  }

  // Reports state (enhanced)
  reports: {
    list: Report[]
    activeReportId: string | null
    editMode: boolean
    collaborators: User[]
    exportQueue: ExportJob[]
    templates: ReportTemplate[]
  }

  // Real-time state
  realTime: RealTimeState

  // User and session
  user: User | null
  session: {
    id: string
    startTime: Date
    lastActivity: Date
  }
}

export interface ReportTemplate {
  id: string
  name: string
  description: string
  category: string
  blocks: Omit<ReportBlock, 'id'>[]
  thumbnail?: string
}

// Action interfaces for better type safety
interface ChatActions {
  // Conversation management
  createConversation: (title: string) => void
  deleteConversation: (conversationId: string) => void
  setActiveConversation: (conversationId: string) => void
  updateConversation: (conversationId: string, updates: Partial<Conversation>) => void

  // Message management
  addMessage: (conversationId: string, message: Omit<ChatMessage, 'id' | 'timestamp' | 'conversationId'>) => void
  updateMessage: (messageId: string, updates: Partial<ChatMessage>) => void
  deleteMessage: (messageId: string) => void
  addMessageReaction: (messageId: string, reaction: Omit<MessageReaction, 'timestamp'>) => void

  // Search and filters
  setChatSearchQuery: (query: string) => void
  setChatFilters: (filters: Partial<ChatFilters>) => void

  // Agent state
  updateAgentState: (state: Partial<AgentState>) => void
}

interface MapActions {
  updateMapViewport: (viewport: Partial<MapViewport>) => void
  toggleMapLayer: (layerId: string) => void
  updateMapLayer: (layerId: string, updates: Partial<MapLayer>) => void
  addMapMarker: (marker: Omit<MapMarker, 'id'>) => void
  updateMapMarker: (markerId: string, updates: Partial<MapMarker>) => void
  deleteMapMarker: (markerId: string) => void
  setMapFilters: (filters: Partial<MapFilters>) => void
  setMapInteractionMode: (mode: 'view' | 'select' | 'draw' | 'measure') => void
  selectMapFeatures: (featureIds: string[]) => void
}

interface ReportActions {
  createReport: (title: string, template?: string) => void
  deleteReport: (reportId: string) => void
  setActiveReport: (reportId: string) => void
  updateReport: (reportId: string, updates: Partial<Report>) => void
  addReportBlock: (reportId: string, block: Omit<ReportBlock, 'id'>) => void
  updateReportBlock: (reportId: string, blockId: string, updates: Partial<ReportBlock>) => void
  deleteReportBlock: (reportId: string, blockId: string) => void
  addReportComment: (reportId: string, blockId: string, comment: Omit<Comment, 'id' | 'timestamp'>) => void
  exportReport: (reportId: string, format: 'pdf' | 'csv' | 'json' | 'docx') => void
}

interface LayoutActions {
  toggleLeftSidebar: () => void
  toggleRightSidebar: () => void
  toggleBottomPanel: () => void
  setActivePanel: (panel: 'chat' | 'map' | 'reports') => void
  setPanelSize: (panel: 'leftSidebar' | 'rightSidebar' | 'bottomPanel', size: number) => void
  toggleSplitView: () => void
}

interface UIActions {
  addNotification: (notification: Omit<Notification, 'id' | 'timestamp'>) => void
  markNotificationRead: (notificationId: string) => void
  removeNotification: (notificationId: string) => void
  openModal: (modal: Omit<Modal, 'id'>) => void
  closeModal: (modalId: string) => void
  setLoading: (key: string, loading: boolean) => void
  setError: (key: string, error: string | null) => void
  setTheme: (theme: 'dark' | 'light' | 'auto') => void
}

// Combined state interface
interface MarketResearchStateWithActions extends MarketResearchState, ChatActions, MapActions, ReportActions, LayoutActions, UIActions {}

// Bangkok coordinates
const BANGKOK_CENTER: [number, number] = [100.5018, 13.7563]

// Initial conversation
const createInitialConversation = (): Conversation => ({
  id: 'initial',
  title: 'Welcome Chat',
  messages: [
    {
      id: '1',
      conversationId: 'initial',
      type: 'assistant',
      content: 'Hello! I\'m your Market Research AI Assistant specialized in the cafe and restaurant industry in Bangkok. How can I help you today?',
      timestamp: new Date(),
      agentState: { status: 'idle' }
    }
  ],
  createdAt: new Date(),
  updatedAt: new Date(),
  participants: [],
  tags: ['welcome'],
  archived: false
})

export const useMarketResearchStore = create<MarketResearchStateWithActions>()(
  devtools(
    persist(
      subscribeWithSelector(
        immer((set, get) => ({
          // Initial state
          layout: {
            leftSidebarCollapsed: false,
            rightSidebarCollapsed: false,
            bottomPanelCollapsed: true,
            activePanel: 'chat',
            splitView: false,
            panelSizes: {
              leftSidebar: 400,
              rightSidebar: 400,
              bottomPanel: 200
            }
          },

          ui: {
            theme: 'dark',
            notifications: [],
            modals: [],
            loading: {},
            errors: {}
          },

          chat: {
            conversations: [createInitialConversation()],
            activeConversationId: 'initial',
            searchQuery: '',
            filters: {},
            isAgentActive: false,
            agentState: {
              status: 'idle',
              todos: [],
              files: {}
            }
          },

          map: {
            viewport: {
              center: BANGKOK_CENTER,
              zoom: 11,
              bearing: 0,
              pitch: 0
            },
            layers: [
              {
                id: 'restaurants',
                name: 'Restaurants',
                type: 'markers',
                visible: true,
                opacity: 1,
                data: []
              },
              {
                id: 'demographics',
                name: 'Demographics',
                type: 'heatmap',
                visible: true,
                opacity: 0.7,
                data: []
              }
            ],
            markers: [],
            selectedFeatures: [],
            filters: {},
            realTimeUpdates: true,
            interactionMode: 'view'
          },

          reports: {
            list: [],
            activeReportId: null,
            editMode: false,
            collaborators: [],
            exportQueue: [],
            templates: []
          },

          realTime: {
            connected: false,
            lastSync: new Date(),
            pendingUpdates: [],
            conflicts: [],
            typingUsers: {}
          },

          user: null,
          session: {
            id: Math.random().toString(36).substr(2, 9),
            startTime: new Date(),
            lastActivity: new Date()
          },

          // Chat Actions
          createConversation: (title) => {
            set((state) => {
              const newConversation: Conversation = {
                id: Math.random().toString(36).substr(2, 9),
                title,
                messages: [],
                createdAt: new Date(),
                updatedAt: new Date(),
                participants: [],
                tags: [],
                archived: false
              }
              state.chat.conversations.push(newConversation)
              state.chat.activeConversationId = newConversation.id
            })
          },

          deleteConversation: (conversationId) => {
            set((state) => {
              state.chat.conversations = state.chat.conversations.filter(c => c.id !== conversationId)
              if (state.chat.activeConversationId === conversationId) {
                state.chat.activeConversationId = state.chat.conversations[0]?.id || null
              }
            })
          },

          setActiveConversation: (conversationId) => {
            set((state) => {
              state.chat.activeConversationId = conversationId
            })
          },

          updateConversation: (conversationId, updates) => {
            set((state) => {
              const conversation = state.chat.conversations.find(c => c.id === conversationId)
              if (conversation) {
                Object.assign(conversation, updates, { updatedAt: new Date() })
              }
            })
          },

          addMessage: (conversationId, message) => {
            set((state) => {
              const conversation = state.chat.conversations.find(c => c.id === conversationId)
              if (conversation) {
                const newMessage: ChatMessage = {
                  ...message,
                  id: Math.random().toString(36).substr(2, 9),
                  conversationId,
                  timestamp: new Date()
                }
                conversation.messages.push(newMessage)
                conversation.updatedAt = new Date()
              }
            })
          },

          updateMessage: (messageId, updates) => {
            set((state) => {
              for (const conversation of state.chat.conversations) {
                const message = conversation.messages.find(m => m.id === messageId)
                if (message) {
                  Object.assign(message, updates)
                  conversation.updatedAt = new Date()
                  break
                }
              }
            })
          },

          deleteMessage: (messageId) => {
            set((state) => {
              for (const conversation of state.chat.conversations) {
                const messageIndex = conversation.messages.findIndex(m => m.id === messageId)
                if (messageIndex !== -1) {
                  conversation.messages.splice(messageIndex, 1)
                  conversation.updatedAt = new Date()
                  break
                }
              }
            })
          },

          addMessageReaction: (messageId, reaction) => {
            set((state) => {
              for (const conversation of state.chat.conversations) {
                const message = conversation.messages.find(m => m.id === messageId)
                if (message) {
                  if (!message.reactions) message.reactions = []
                  message.reactions.push({ ...reaction, timestamp: new Date() })
                  conversation.updatedAt = new Date()
                  break
                }
              }
            })
          },

          setChatSearchQuery: (query) => {
            set((state) => {
              state.chat.searchQuery = query
            })
          },

          setChatFilters: (filters) => {
            set((state) => {
              state.chat.filters = { ...state.chat.filters, ...filters }
            })
          },

          updateAgentState: (newState) => {
            set((state) => {
              state.chat.agentState = { ...state.chat.agentState, ...newState }
            })
          },

          // Map Actions
          updateMapViewport: (viewport) => {
            set((state) => {
              state.map.viewport = { ...state.map.viewport, ...viewport }
            })
          },

          toggleMapLayer: (layerId) => {
            set((state) => {
              const layer = state.map.layers.find(l => l.id === layerId)
              if (layer) {
                layer.visible = !layer.visible
              }
            })
          },

          updateMapLayer: (layerId, updates) => {
            set((state) => {
              const layer = state.map.layers.find(l => l.id === layerId)
              if (layer) {
                Object.assign(layer, updates)
              }
            })
          },

          addMapMarker: (marker) => {
            set((state) => {
              const newMarker: MapMarker = {
                ...marker,
                id: Math.random().toString(36).substr(2, 9)
              }
              state.map.markers.push(newMarker)
            })
          },

          updateMapMarker: (markerId, updates) => {
            set((state) => {
              const marker = state.map.markers.find(m => m.id === markerId)
              if (marker) {
                Object.assign(marker, updates)
              }
            })
          },

          deleteMapMarker: (markerId) => {
            set((state) => {
              state.map.markers = state.map.markers.filter(m => m.id !== markerId)
            })
          },

          setMapFilters: (filters) => {
            set((state) => {
              state.map.filters = { ...state.map.filters, ...filters }
            })
          },

          setMapInteractionMode: (mode) => {
            set((state) => {
              state.map.interactionMode = mode
            })
          },

          selectMapFeatures: (featureIds) => {
            set((state) => {
              state.map.selectedFeatures = featureIds
            })
          },

          // Report Actions
          createReport: (title, template) => {
            set((state) => {
              const newReport: Report = {
                id: Math.random().toString(36).substr(2, 9),
                title,
                description: '',
                content: [],
                createdAt: new Date(),
                updatedAt: new Date(),
                shared: false,
                collaborators: [],
                version: 1,
                tags: [],
                template,
                exportFormats: ['pdf', 'csv', 'json']
              }
              state.reports.list.push(newReport)
              state.reports.activeReportId = newReport.id
            })
          },

          deleteReport: (reportId) => {
            set((state) => {
              state.reports.list = state.reports.list.filter(r => r.id !== reportId)
              if (state.reports.activeReportId === reportId) {
                state.reports.activeReportId = state.reports.list[0]?.id || null
              }
            })
          },

          setActiveReport: (reportId) => {
            set((state) => {
              state.reports.activeReportId = reportId
            })
          },

          updateReport: (reportId, updates) => {
            set((state) => {
              const report = state.reports.list.find(r => r.id === reportId)
              if (report) {
                Object.assign(report, updates, { updatedAt: new Date(), version: report.version + 1 })
              }
            })
          },

          addReportBlock: (reportId, block) => {
            set((state) => {
              const report = state.reports.list.find(r => r.id === reportId)
              if (report) {
                const newBlock: ReportBlock = {
                  ...block,
                  id: Math.random().toString(36).substr(2, 9),
                  position: block.position || { x: 0, y: report.content.length * 100, width: 100, height: 100 }
                }
                report.content.push(newBlock)
                report.updatedAt = new Date()
                report.version += 1
              }
            })
          },

          updateReportBlock: (reportId, blockId, updates) => {
            set((state) => {
              const report = state.reports.list.find(r => r.id === reportId)
              if (report) {
                const block = report.content.find(b => b.id === blockId)
                if (block) {
                  Object.assign(block, updates)
                  report.updatedAt = new Date()
                  report.version += 1
                }
              }
            })
          },

          deleteReportBlock: (reportId, blockId) => {
            set((state) => {
              const report = state.reports.list.find(r => r.id === reportId)
              if (report) {
                report.content = report.content.filter(b => b.id !== blockId)
                report.updatedAt = new Date()
                report.version += 1
              }
            })
          },

          addReportComment: (reportId, blockId, comment) => {
            set((state) => {
              const report = state.reports.list.find(r => r.id === reportId)
              if (report) {
                const block = report.content.find(b => b.id === blockId)
                if (block) {
                  if (!block.comments) block.comments = []
                  const newComment: Comment = {
                    ...comment,
                    id: Math.random().toString(36).substr(2, 9),
                    timestamp: new Date()
                  }
                  block.comments.push(newComment)
                  report.updatedAt = new Date()
                }
              }
            })
          },

          exportReport: (reportId, format) => {
            set((state) => {
              const exportJob: ExportJob = {
                id: Math.random().toString(36).substr(2, 9),
                reportId,
                format,
                status: 'pending',
                progress: 0
              }
              state.reports.exportQueue.push(exportJob)
            })
          },

          // Layout Actions
          toggleLeftSidebar: () => {
            set((state) => {
              state.layout.leftSidebarCollapsed = !state.layout.leftSidebarCollapsed
            })
          },

          toggleRightSidebar: () => {
            set((state) => {
              state.layout.rightSidebarCollapsed = !state.layout.rightSidebarCollapsed
            })
          },

          toggleBottomPanel: () => {
            set((state) => {
              state.layout.bottomPanelCollapsed = !state.layout.bottomPanelCollapsed
            })
          },

          setActivePanel: (panel) => {
            set((state) => {
              state.layout.activePanel = panel
            })
          },

          setPanelSize: (panel, size) => {
            set((state) => {
              state.layout.panelSizes[panel] = size
            })
          },

          toggleSplitView: () => {
            set((state) => {
              state.layout.splitView = !state.layout.splitView
            })
          },

          // UI Actions
          addNotification: (notification) => {
            set((state) => {
              const newNotification: Notification = {
                ...notification,
                id: Math.random().toString(36).substr(2, 9),
                timestamp: new Date(),
                read: false
              }
              state.ui.notifications.push(newNotification)
            })
          },

          markNotificationRead: (notificationId) => {
            set((state) => {
              const notification = state.ui.notifications.find(n => n.id === notificationId)
              if (notification) {
                notification.read = true
              }
            })
          },

          removeNotification: (notificationId) => {
            set((state) => {
              state.ui.notifications = state.ui.notifications.filter(n => n.id !== notificationId)
            })
          },

          openModal: (modal) => {
            set((state) => {
              const newModal: Modal = {
                ...modal,
                id: Math.random().toString(36).substr(2, 9)
              }
              state.ui.modals.push(newModal)
            })
          },

          closeModal: (modalId) => {
            set((state) => {
              state.ui.modals = state.ui.modals.filter(m => m.id !== modalId)
            })
          },

          setLoading: (key, loading) => {
            set((state) => {
              if (loading) {
                state.ui.loading[key] = true
              } else {
                delete state.ui.loading[key]
              }
            })
          },

          setError: (key, error) => {
            set((state) => {
              if (error) {
                state.ui.errors[key] = error
              } else {
                delete state.ui.errors[key]
              }
            })
          },

          setTheme: (theme) => {
            set((state) => {
              state.ui.theme = theme
            })
          }
        }))
      ),
      {
        name: 'market-research-store',
        partialize: (state) => ({
          layout: state.layout,
          ui: { theme: state.ui.theme },
          chat: {
            conversations: state.chat.conversations,
            activeConversationId: state.chat.activeConversationId
          },
          map: {
            viewport: state.map.viewport,
            layers: state.map.layers.map(l => ({ ...l, data: [] })) // Don't persist large data
          },
          reports: {
            list: state.reports.list,
            activeReportId: state.reports.activeReportId
          }
        })
      }
    ),
    {
      name: 'market-research-store'
    }
  )
)

// Enhanced selectors for easier state access
export const useLayout = () => useMarketResearchStore((state) => state.layout)
export const useUI = () => useMarketResearchStore((state) => state.ui)
export const useChat = () => useMarketResearchStore((state) => state.chat)
export const useMap = () => useMarketResearchStore((state) => state.map)
export const useReports = () => useMarketResearchStore((state) => state.reports)
export const useRealTime = () => useMarketResearchStore((state) => state.realTime)

// Specific selectors for common use cases
export const useActiveConversation = () => useMarketResearchStore((state) => {
  const { conversations, activeConversationId } = state.chat
  return conversations.find(c => c.id === activeConversationId) || null
})

export const useActiveReport = () => useMarketResearchStore((state) => {
  const { list, activeReportId } = state.reports
  return list.find(r => r.id === activeReportId) || null
})

export const useNotifications = () => useMarketResearchStore((state) =>
  state.ui.notifications.filter(n => !n.read)
)

export const useMapLayers = () => useMarketResearchStore((state) =>
  state.map.layers.filter(l => l.visible)
)

export const useAgentState = () => useMarketResearchStore((state) => state.chat.agentState)