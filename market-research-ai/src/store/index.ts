import { create } from 'zustand'
import { subscribeWithSelector } from 'zustand/middleware'

// Core application state interfaces
export interface MarketResearchData {
  demographics: any[]
  competitors: any[]
  marketTrends: any[]
  locationData: any[]
  reports: Report[]
}

export interface Report {
  id: string
  title: string
  content: ReportBlock[]
  createdAt: Date
  updatedAt: Date
  shared: boolean
}

export interface ReportBlock {
  id: string
  type: 'text' | 'chart' | 'image' | 'heading' | 'list' | 'quote' | 'ai-suggestion'
  content: any
  metadata?: any
}

export interface ChatMessage {
  id: string
  type: 'user' | 'assistant' | 'system'
  content: string
  timestamp: Date
  agentState?: AgentState
  tools?: string[]
}

export interface AgentState {
  status: 'idle' | 'thinking' | 'searching' | 'analyzing' | 'generating'
  currentTool?: string
  progress?: number
  todos?: Array<{
    id: string
    content: string
    status: 'pending' | 'in_progress' | 'completed'
  }>
  files?: Record<string, string>
}

export interface MapState {
  center: [number, number] // [lng, lat] for Bangkok
  zoom: number
  selectedLayers: string[]
  markers: Array<{
    id: string
    coordinates: [number, number]
    type: 'restaurant' | 'cafe' | 'competitor' | 'opportunity'
    data: any
  }>
  heatmapData: any[]
}

// Main application state
interface MarketResearchState {
  // Chat state
  messages: ChatMessage[]
  isAgentActive: boolean
  agentState: AgentState
  
  // Map state
  mapState: MapState
  
  // Report state
  currentReport: Report | null
  reports: Report[]
  
  // Research data
  researchData: MarketResearchData
  
  // UI state
  activeView: 'chat' | 'map' | 'report'
  sidebarCollapsed: boolean
  
  // Actions
  addMessage: (message: Omit<ChatMessage, 'id' | 'timestamp'>) => void
  updateAgentState: (state: Partial<AgentState>) => void
  updateMapState: (state: Partial<MapState>) => void
  createReport: (title: string) => void
  updateReport: (reportId: string, updates: Partial<Report>) => void
  addReportBlock: (reportId: string, block: Omit<ReportBlock, 'id'>) => void
  updateReportBlock: (reportId: string, blockId: string, updates: Partial<ReportBlock>) => void
  deleteReportBlock: (reportId: string, blockId: string) => void
  setActiveView: (view: 'chat' | 'map' | 'report') => void
  setSidebarCollapsed: (collapsed: boolean) => void
  updateResearchData: (data: Partial<MarketResearchData>) => void
}

// Bangkok coordinates
const BANGKOK_CENTER: [number, number] = [100.5018, 13.7563]

export const useMarketResearchStore = create<MarketResearchState>()(
  subscribeWithSelector((set, get) => ({
    // Initial state
    messages: [
      {
        id: '1',
        type: 'assistant',
        content: 'Hello! I\'m your Market Research AI Assistant specialized in the cafe and restaurant industry in Bangkok. How can I help you today?',
        timestamp: new Date(),
        agentState: { status: 'idle' }
      }
    ],
    isAgentActive: false,
    agentState: {
      status: 'idle',
      todos: [],
      files: {}
    },
    mapState: {
      center: BANGKOK_CENTER,
      zoom: 11,
      selectedLayers: ['restaurants', 'demographics'],
      markers: [],
      heatmapData: []
    },
    currentReport: null,
    reports: [],
    researchData: {
      demographics: [],
      competitors: [],
      marketTrends: [],
      locationData: [],
      reports: []
    },
    activeView: 'chat',
    sidebarCollapsed: false,

    // Actions
    addMessage: (message) => {
      const newMessage: ChatMessage = {
        ...message,
        id: Math.random().toString(36).substr(2, 9),
        timestamp: new Date()
      }
      set((state) => ({
        messages: [...state.messages, newMessage]
      }))
    },

    updateAgentState: (newState) => {
      set((state) => ({
        agentState: { ...state.agentState, ...newState }
      }))
    },

    updateMapState: (newState) => {
      set((state) => ({
        mapState: { ...state.mapState, ...newState }
      }))
    },

    createReport: (title) => {
      const newReport: Report = {
        id: Math.random().toString(36).substr(2, 9),
        title,
        content: [],
        createdAt: new Date(),
        updatedAt: new Date(),
        shared: false
      }
      set((state) => ({
        reports: [...state.reports, newReport],
        currentReport: newReport
      }))
    },

    updateReport: (reportId, updates) => {
      set((state) => ({
        reports: state.reports.map(report =>
          report.id === reportId
            ? { ...report, ...updates, updatedAt: new Date() }
            : report
        ),
        currentReport: state.currentReport?.id === reportId
          ? { ...state.currentReport, ...updates, updatedAt: new Date() }
          : state.currentReport
      }))
    },

    addReportBlock: (reportId, block) => {
      const newBlock: ReportBlock = {
        ...block,
        id: Math.random().toString(36).substr(2, 9)
      }
      set((state) => ({
        reports: state.reports.map(report =>
          report.id === reportId
            ? { ...report, content: [...report.content, newBlock], updatedAt: new Date() }
            : report
        ),
        currentReport: state.currentReport?.id === reportId
          ? { ...state.currentReport, content: [...state.currentReport.content, newBlock], updatedAt: new Date() }
          : state.currentReport
      }))
    },

    updateReportBlock: (reportId, blockId, updates) => {
      set((state) => ({
        reports: state.reports.map(report =>
          report.id === reportId
            ? {
                ...report,
                content: report.content.map(block =>
                  block.id === blockId ? { ...block, ...updates } : block
                ),
                updatedAt: new Date()
              }
            : report
        ),
        currentReport: state.currentReport?.id === reportId
          ? {
              ...state.currentReport,
              content: state.currentReport.content.map(block =>
                block.id === blockId ? { ...block, ...updates } : block
              ),
              updatedAt: new Date()
            }
          : state.currentReport
      }))
    },

    deleteReportBlock: (reportId, blockId) => {
      set((state) => ({
        reports: state.reports.map(report =>
          report.id === reportId
            ? {
                ...report,
                content: report.content.filter(block => block.id !== blockId),
                updatedAt: new Date()
              }
            : report
        ),
        currentReport: state.currentReport?.id === reportId
          ? {
              ...state.currentReport,
              content: state.currentReport.content.filter(block => block.id !== blockId),
              updatedAt: new Date()
            }
          : state.currentReport
      }))
    },

    setActiveView: (view) => {
      set({ activeView: view })
    },

    setSidebarCollapsed: (collapsed) => {
      set({ sidebarCollapsed: collapsed })
    },

    updateResearchData: (data) => {
      set((state) => ({
        researchData: { ...state.researchData, ...data }
      }))
    }
  }))
)

// Selectors for easier state access
export const useMessages = () => useMarketResearchStore((state) => state.messages)
export const useAgentState = () => useMarketResearchStore((state) => state.agentState)
export const useMapState = () => useMarketResearchStore((state) => state.mapState)
export const useCurrentReport = () => useMarketResearchStore((state) => state.currentReport)
export const useActiveView = () => useMarketResearchStore((state) => state.activeView)