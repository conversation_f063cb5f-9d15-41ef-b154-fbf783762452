'use client'

import { motion, AnimatePresence } from 'framer-motion'
import { useMarketResearchStore } from '@/store'
import ChatInterface from '@/components/chat/ChatInterface'
import ReportView from '@/components/report/ReportView'
import Header from '@/components/shared/Header'

export default function Home() {
  const { activeView } = useMarketResearchStore()

  const pageVariants = {
    initial: { 
      opacity: 0, 
      x: 100,
      scale: 0.95 
    },
    in: { 
      opacity: 1, 
      x: 0,
      scale: 1 
    },
    out: { 
      opacity: 0, 
      x: -100,
      scale: 0.95 
    }
  }

  const pageTransition = {
    type: "tween",
    ease: "anticipate",
    duration: 0.6
  }

  return (
    <div className="h-screen text-white font-sans flex flex-col overflow-hidden">
      {/* Animated background */}
      <div className="fixed inset-0 bg-gradient-to-br from-background-start via-background-end to-background-start">
        {/* Animated mesh gradient overlay */}
        <motion.div 
          className="absolute inset-0 opacity-30"
          animate={{
            background: [
              "radial-gradient(circle at 20% 50%, rgba(99, 102, 241, 0.3) 0%, transparent 50%)",
              "radial-gradient(circle at 80% 50%, rgba(139, 92, 246, 0.3) 0%, transparent 50%)",
              "radial-gradient(circle at 50% 20%, rgba(99, 102, 241, 0.3) 0%, transparent 50%)",
            ]
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
      </div>

      {/* Header */}
      <Header />
      
      {/* Main content with page transitions */}
      <main className="flex-1 flex overflow-hidden relative z-10">
        <AnimatePresence mode="wait">
          {activeView === 'chat' && (
            <motion.div
              key="chat"
              className="w-full"
              initial="initial"
              animate="in"
              exit="out"
              variants={pageVariants}
              transition={pageTransition}
            >
              <ChatInterface />
            </motion.div>
          )}
          
          {activeView === 'report' && (
            <motion.div
              key="report"
              className="w-full"
              initial="initial"
              animate="in"
              exit="out"
              variants={pageVariants}
              transition={pageTransition}
            >
              <ReportView />
            </motion.div>
          )}
        </AnimatePresence>
      </main>

      {/* Loading overlay for page transitions */}
      <AnimatePresence>
        {/* This could be used for loading states */}
      </AnimatePresence>
    </div>
  )
}
