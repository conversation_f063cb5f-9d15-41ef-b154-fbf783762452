@tailwind base;
@tailwind components;
@tailwind utilities;

/* Mapbox CSS */
@import 'mapbox-gl/dist/mapbox-gl.css';

/* Enhanced Design System */
:root {
  /* Core Colors */
  --primary-400: #8B5CF6;
  --primary-500: #6366F1;
  --primary-600: #5855EB;
  --secondary-500: #F59E0B;
  --success-500: #10B981;
  
  /* Background System */
  --bg-primary: #0F172A;
  --bg-secondary: #1E293B;
  --bg-tertiary: #334155;
  
  /* Glass Morphism */
  --glass-bg: rgba(30, 41, 59, 0.8);
  --glass-border: rgba(148, 163, 184, 0.1);
  --glass-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
  
  /* Text Colors */
  --text-primary: #F8FAFC;
  --text-secondary: #E2E8F0;
  --text-muted: #94A3B8;
}

* {
  box-sizing: border-box;
}

html {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

body {
  margin: 0;
  padding: 0;
  min-height: 100vh;
  background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
  color: var(--text-primary);
  font-family: inherit;
  line-height: 1.5;
}

/* Glass Components */
.glass {
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid var(--glass-border);
  box-shadow: var(--glass-shadow);
  border-radius: 1rem;
}

.glass-panel {
  background: rgba(30, 41, 59, 0.6);
  backdrop-filter: blur(16px);
  -webkit-backdrop-filter: blur(16px);
  border: 1px solid rgba(148, 163, 184, 0.2);
  border-radius: 0.75rem;
}

/* Button Styles */
.btn-primary {
  background: linear-gradient(135deg, var(--primary-500) 0%, var(--primary-400) 100%);
  color: white;
  border: none;
  border-radius: 0.5rem;
  padding: 0.75rem 1.5rem;
  font-weight: 600;
  transition: all 0.2s ease;
  cursor: pointer;
}

.btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 10px 25px rgba(99, 102, 241, 0.3);
}

.btn-secondary {
  background: rgba(148, 163, 184, 0.1);
  color: var(--text-secondary);
  border: 1px solid rgba(148, 163, 184, 0.2);
  border-radius: 0.5rem;
  padding: 0.75rem 1.5rem;
  font-weight: 500;
  transition: all 0.2s ease;
  cursor: pointer;
}

.btn-secondary:hover {
  background: rgba(148, 163, 184, 0.2);
  border-color: rgba(148, 163, 184, 0.3);
}

/* Input Styles */
.input-primary {
  background: rgba(30, 41, 59, 0.6);
  border: 1px solid rgba(148, 163, 184, 0.2);
  border-radius: 0.75rem;
  padding: 0.75rem 1rem;
  color: var(--text-primary);
  font-size: 0.875rem;
  transition: all 0.2s ease;
  width: 100%;
}

.input-primary:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.input-primary::placeholder {
  color: var(--text-muted);
}

/* Message Bubble Styles */
.message-user {
  background: linear-gradient(135deg, var(--primary-500) 0%, var(--primary-400) 100%);
  color: white;
  border-radius: 1rem 1rem 0.25rem 1rem;
  padding: 0.875rem 1.25rem;
  margin-left: 2rem;
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
}

.message-assistant {
  background: var(--glass-bg);
  backdrop-filter: blur(16px);
  -webkit-backdrop-filter: blur(16px);
  border: 1px solid var(--glass-border);
  color: var(--text-primary);
  border-radius: 1rem 1rem 1rem 0.25rem;
  padding: 0.875rem 1.25rem;
  margin-right: 2rem;
}

/* Agent Status */
.agent-status {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: rgba(16, 185, 129, 0.1);
  border: 1px solid rgba(16, 185, 129, 0.2);
  border-radius: 2rem;
  font-size: 0.75rem;
  font-weight: 600;
  color: var(--success-500);
}

.agent-status.thinking {
  background: rgba(245, 158, 11, 0.1);
  border-color: rgba(245, 158, 11, 0.2);
  color: var(--secondary-500);
}

.agent-status.searching {
  background: rgba(99, 102, 241, 0.1);
  border-color: rgba(99, 102, 241, 0.2);
  color: var(--primary-500);
}

/* Floating Elements */
.floating {
  position: relative;
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-6px); }
}

/* Pulse Animation */
.pulse-glow {
  animation: pulseGlow 2s ease-in-out infinite;
}

@keyframes pulseGlow {
  0%, 100% { 
    box-shadow: 0 0 20px rgba(99, 102, 241, 0.3);
  }
  50% { 
    box-shadow: 0 0 40px rgba(139, 92, 246, 0.6);
  }
}

/* Scrollbar Styling */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(148, 163, 184, 0.1);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, var(--primary-500) 0%, var(--primary-400) 100%);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, var(--primary-400) 0%, var(--primary-500) 100%);
}

/* Utility Classes */
.text-gradient {
  background: linear-gradient(135deg, var(--primary-500) 0%, var(--primary-400) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hover-lift {
  transition: transform 0.2s ease;
}

.hover-lift:hover {
  transform: translateY(-2px);
}

/* Material Icons */
.material-symbols-outlined {
  font-variation-settings: 'FILL' 0, 'wght' 400, 'GRAD' 0, 'opsz' 24;
  font-size: 1.25rem;
  line-height: 1;
}